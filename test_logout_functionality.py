#!/usr/bin/env python3
"""
测试微信公众号注销功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_service import WeChatMPService

async def test_logout_functionality():
    """测试注销功能"""
    print("=== 测试微信公众号注销功能 ===")
    
    # 测试账号ID
    test_account_id = 1
    
    # 创建服务实例
    wechat_service = WeChatMPService(account_id=test_account_id)
    
    try:
        print(f"\n1. 测试正常注销功能...")
        
        # 首先检查是否有保存的登录状态
        has_saved_state = await wechat_service.load_login_state()
        
        if has_saved_state:
            print("✅ 找到已保存的登录状态")
            
            # 检查登录是否有效
            is_valid = await wechat_service.check_existing_login()
            print(f"登录状态有效性: {'有效' if is_valid else '无效'}")
            
            # 执行注销
            print("\n开始执行注销...")
            logout_success = await wechat_service.logout(clear_saved_state=True)
            
            if logout_success:
                print("✅ 注销成功")
            else:
                print("⚠️ 注销完成，但可能存在部分问题")
            
            # 验证注销效果
            print("\n验证注销效果...")
            state_file = os.path.join(wechat_service.user_data_dir, "login_state.json")
            if not os.path.exists(state_file):
                print("✅ 登录状态文件已被删除")
            else:
                print("❌ 登录状态文件仍然存在")
            
        else:
            print("ℹ️ 没有找到已保存的登录状态，创建一个测试状态...")
            
            # 创建测试登录状态
            await wechat_service.get_login_qrcode()
            print("请扫码登录以创建测试状态，或按 Ctrl+C 跳过...")
            
            try:
                login_success = await wechat_service.wait_for_login_complete(max_wait_time=60)
                if login_success:
                    print("✅ 登录成功，现在测试注销...")
                    logout_success = await wechat_service.logout(clear_saved_state=True)
                    print(f"注销结果: {'成功' if logout_success else '部分成功'}")
                else:
                    print("⏭️ 登录超时，跳过注销测试")
            except KeyboardInterrupt:
                print("\n⏭️ 用户跳过登录，继续测试强制注销...")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        await wechat_service.close()

async def test_force_logout():
    """测试强制注销功能"""
    print("\n=== 测试强制注销功能 ===")
    
    test_account_id = 2
    wechat_service = WeChatMPService(account_id=test_account_id)
    
    try:
        print("执行强制注销...")
        force_logout_success = await wechat_service.force_logout()
        
        if force_logout_success:
            print("✅ 强制注销成功")
        else:
            print("❌ 强制注销失败")
        
        # 验证强制注销效果
        state_file = os.path.join(wechat_service.user_data_dir, "login_state.json")
        if not os.path.exists(state_file):
            print("✅ 强制注销：登录状态文件已被删除")
        else:
            print("⚠️ 强制注销：登录状态文件仍然存在")
            
    except Exception as e:
        print(f"❌ 强制注销测试中发生错误: {e}")
    finally:
        await wechat_service.close()

async def test_multiple_logout():
    """测试多账号注销"""
    print("\n=== 测试多账号注销功能 ===")
    
    account_ids = [3, 4, 5]
    
    for account_id in account_ids:
        print(f"\n处理账号 {account_id}...")
        wechat_service = WeChatMPService(account_id=account_id)
        
        try:
            # 创建一个测试状态文件（模拟已登录状态）
            state_file = os.path.join(wechat_service.user_data_dir, "login_state.json")
            os.makedirs(wechat_service.user_data_dir, exist_ok=True)
            
            test_state = {
                "account_id": account_id,
                "saved_at": "2025-07-18T10:00:00",
                "cookies": [],
                "storage_state": {"cookies": [], "origins": []}
            }
            
            import json
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(test_state, f)
            
            print(f"✅ 为账号 {account_id} 创建了测试登录状态")
            
            # 执行强制注销
            force_logout_success = await wechat_service.force_logout()
            print(f"账号 {account_id} 强制注销: {'成功' if force_logout_success else '失败'}")
            
        except Exception as e:
            print(f"❌ 账号 {account_id} 处理失败: {e}")
        finally:
            await wechat_service.close()

async def test_logout_edge_cases():
    """测试注销的边缘情况"""
    print("\n=== 测试注销边缘情况 ===")
    
    # 测试1: 没有account_id的情况
    print("\n测试1: 没有account_id的服务实例...")
    wechat_service = WeChatMPService()  # 没有account_id
    
    try:
        logout_success = await wechat_service.logout()
        print(f"无account_id注销: {'成功' if logout_success else '失败'}")
    except Exception as e:
        print(f"无account_id注销异常: {e}")
    finally:
        await wechat_service.close()
    
    # 测试2: 不存在的账号ID
    print("\n测试2: 不存在的账号ID...")
    wechat_service = WeChatMPService(account_id=99999)
    
    try:
        logout_success = await wechat_service.logout()
        print(f"不存在账号注销: {'成功' if logout_success else '失败'}")
    except Exception as e:
        print(f"不存在账号注销异常: {e}")
    finally:
        await wechat_service.close()
    
    # 测试3: 只清理保存状态，不清理浏览器会话
    print("\n测试3: 只清理保存状态...")
    wechat_service = WeChatMPService(account_id=6)
    
    try:
        # 创建测试状态文件
        state_file = os.path.join(wechat_service.user_data_dir, "login_state.json")
        os.makedirs(wechat_service.user_data_dir, exist_ok=True)
        
        test_state = {"account_id": 6, "saved_at": "2025-07-18T10:00:00"}
        import json
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(test_state, f)
        
        # 只清理保存状态
        logout_success = await wechat_service.logout(clear_saved_state=True)
        print(f"清理保存状态: {'成功' if logout_success else '失败'}")
        
        # 验证文件是否被删除
        if not os.path.exists(state_file):
            print("✅ 保存状态文件已删除")
        else:
            print("❌ 保存状态文件仍存在")
            
    except Exception as e:
        print(f"清理保存状态测试异常: {e}")
    finally:
        await wechat_service.close()

async def main():
    """主测试函数"""
    print("选择测试模式:")
    print("1. 测试正常注销功能")
    print("2. 测试强制注销功能")
    print("3. 测试多账号注销")
    print("4. 测试边缘情况")
    print("5. 运行所有测试")
    
    choice = input("请输入选择 (1-5): ").strip()
    
    if choice == "1":
        await test_logout_functionality()
    elif choice == "2":
        await test_force_logout()
    elif choice == "3":
        await test_multiple_logout()
    elif choice == "4":
        await test_logout_edge_cases()
    elif choice == "5":
        print("🚀 开始运行所有测试...")
        await test_logout_functionality()
        await test_force_logout()
        await test_multiple_logout()
        await test_logout_edge_cases()
        print("\n🎉 所有测试完成！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    asyncio.run(main())
