from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional
from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_service import DataService
from app.models import PlatformAccount

router = APIRouter()

@router.get("/dashboard")
async def get_dashboard_data(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取Dashboard概览数据"""
    
    summary = DataService.get_dashboard_summary(db, current_user.id)
    return summary

@router.get("/user-growth/{account_id}")
async def get_user_growth_trend(
    account_id: int,
    days: Optional[int] = 30,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户增长趋势"""
    
    # 验证账号属于当前用户
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    trend_data = DataService.get_user_growth_trend(db, account_id, days)
    
    return {
        "account_id": account_id,
        "account_name": account.name,
        "days": days,
        "trend_data": trend_data
    }

@router.get("/article-performance/{account_id}")
async def get_article_performance(
    account_id: int,
    days: Optional[int] = 30,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取文章表现数据"""
    
    # 验证账号属于当前用户
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    article_data = DataService.get_article_performance(db, account_id, days)
    
    return {
        "account_id": account_id,
        "account_name": account.name,
        "days": days,
        "article_data": article_data
    }

@router.get("/platform-stats")
async def get_platform_statistics(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取平台统计数据"""
    
    stats = DataService.get_platform_statistics(db, current_user.id)
    return stats

@router.get("/top-articles/{account_id}")
async def get_top_articles(
    account_id: int,
    limit: Optional[int] = 10,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取热门文章"""
    
    # 验证账号属于当前用户
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    top_articles = DataService.get_top_articles(db, account_id, limit)
    
    return {
        "account_id": account_id,
        "account_name": account.name,
        "limit": limit,
        "top_articles": top_articles
    }
