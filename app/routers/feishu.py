from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import logging
from typing import Optional

from app.database import get_db
from app.models import PlatformAccount, User
from app.services.feishu_service import FeishuService
from app.services.data_converter import WeChatDataConverter
from app.services.wechat_service import WeChatMPService
from app.services.auth_service import get_current_user

router = APIRouter(prefix="/api/feishu", tags=["feishu"])
logger = logging.getLogger(__name__)

@router.post("/create-bitable/{account_id}")
async def create_bitable_for_account(
    account_id: int,
    bitable_name: Optional[str] = None,
    folder_token: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """为指定的微信公众号账号创建飞书多维表格"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        # 检查是否已经创建过飞书表格
        if account.feishu_app_token:
            return {
                "success": True,
                "message": "该账号已有飞书多维表格",
                "app_token": account.feishu_app_token,
                "table_id": account.feishu_table_id
            }
        
        # 初始化飞书服务
        feishu_service = FeishuService()
        
        # 创建多维表格
        if not bitable_name:
            bitable_name = f"{account.name}_数据分析表格"
        
        app_token = await feishu_service.create_bitable(bitable_name, folder_token)
        if not app_token:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建多维表格失败"
            )
        
        # 创建用户概况数据表
        user_table_id = await feishu_service.create_table(app_token, "用户概况数据")
        if not user_table_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建用户概况数据表失败"
            )
        
        # 创建图文分析数据表
        article_table_id = await feishu_service.create_table(app_token, "图文分析数据")
        if not article_table_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建图文分析数据表失败"
            )
        
        # 保存飞书信息到数据库
        account.feishu_app_token = app_token
        account.feishu_table_id = user_table_id  # 主要使用用户概况表ID
        account.feishu_record_ids = {
            "user_table_id": user_table_id,
            "article_table_id": article_table_id,
            "record_ids": []
        }
        account.feishu_created_at = datetime.now(timezone.utc)
        
        db.commit()
        
        logger.info(f"成功为账号{account_id}创建飞书多维表格: {app_token}")
        
        return {
            "success": True,
            "message": "飞书多维表格创建成功",
            "app_token": app_token,
            "user_table_id": user_table_id,
            "article_table_id": article_table_id,
            "bitable_name": bitable_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞书多维表格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建飞书多维表格失败: {str(e)}"
        )

@router.post("/sync-data/{account_id}")
async def sync_wechat_data_to_feishu(
    account_id: int,
    begin_date: str,
    end_date: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """同步微信公众号数据到飞书多维表格"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        # 检查是否已创建飞书表格
        if not account.feishu_app_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先创建飞书多维表格"
            )
        
        # 下载微信数据
        wechat_service = WeChatMPService(account_id)
        excel_data = await wechat_service.download_data_excel(begin_date, end_date)
        
        if not excel_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="下载微信数据失败，请检查登录状态"
            )
        
        # 解析Excel数据
        parsed_data = WeChatDataConverter.parse_excel_data(excel_data)
        if not parsed_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="解析微信数据失败"
            )
        
        # 初始化飞书服务
        feishu_service = FeishuService()
        
        # 获取表格ID信息
        feishu_info = account.feishu_record_ids or {}
        user_table_id = feishu_info.get("user_table_id", account.feishu_table_id)
        article_table_id = feishu_info.get("article_table_id")
        
        sync_results = {
            "user_data_synced": 0,
            "article_data_synced": 0,
            "errors": []
        }
        
        # 同步用户概况数据
        if parsed_data["user_summary"] and user_table_id:
            user_records = WeChatDataConverter.prepare_feishu_records(
                "user_summary", 
                parsed_data["user_summary"]
            )
            
            if user_records:
                record_ids = await feishu_service.batch_create_records(
                    account.feishu_app_token,
                    user_table_id,
                    user_records
                )
                
                if record_ids:
                    sync_results["user_data_synced"] = len(record_ids)
                    # 更新记录ID
                    if "record_ids" not in feishu_info:
                        feishu_info["record_ids"] = []
                    feishu_info["record_ids"].extend(record_ids)
                else:
                    sync_results["errors"].append("用户概况数据同步失败")
        
        # 同步图文分析数据
        if parsed_data["article_summary"] and article_table_id:
            article_records = WeChatDataConverter.prepare_feishu_records(
                "article_summary",
                parsed_data["article_summary"]
            )
            
            if article_records:
                record_ids = await feishu_service.batch_create_records(
                    account.feishu_app_token,
                    article_table_id,
                    article_records
                )
                
                if record_ids:
                    sync_results["article_data_synced"] = len(record_ids)
                    # 更新记录ID
                    if "record_ids" not in feishu_info:
                        feishu_info["record_ids"] = []
                    feishu_info["record_ids"].extend(record_ids)
                else:
                    sync_results["errors"].append("图文分析数据同步失败")
        
        # 更新数据库
        account.feishu_record_ids = feishu_info
        db.commit()
        
        # 清理微信服务资源
        await wechat_service.close()
        
        logger.info(f"账号{account_id}数据同步完成: {sync_results}")
        
        return {
            "success": True,
            "message": "数据同步完成",
            "sync_results": sync_results,
            "data_summary": {
                "user_records": len(parsed_data["user_summary"]),
                "article_records": len(parsed_data["article_summary"]),
                "sheets_found": parsed_data["raw_sheets"]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步数据到飞书失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步数据失败: {str(e)}"
        )

@router.get("/bitable-info/{account_id}")
async def get_bitable_info(
    account_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取账号的飞书多维表格信息"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        if not account.feishu_app_token:
            return {
                "success": True,
                "has_bitable": False,
                "message": "尚未创建飞书多维表格"
            }
        
        # 获取飞书信息
        feishu_info = account.feishu_record_ids or {}
        
        return {
            "success": True,
            "has_bitable": True,
            "app_token": account.feishu_app_token,
            "user_table_id": feishu_info.get("user_table_id", account.feishu_table_id),
            "article_table_id": feishu_info.get("article_table_id"),
            "record_count": len(feishu_info.get("record_ids", [])),
            "created_at": account.feishu_created_at.isoformat() if account.feishu_created_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取飞书表格信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取飞书表格信息失败: {str(e)}"
        )
