from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    accounts = relationship("PlatformAccount", back_populates="owner")

class PlatformAccount(Base):
    __tablename__ = "platform_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    platform = Column(String(50))  # wechat_mp, wechat_service, xiaohongshu
    user_id = Column(Integer, ForeignKey("users.id"))
    login_status = Column(Boolean, default=False)
    last_login_time = Column(DateTime)
    cookies = Column(Text)  # 存储登录cookie
    created_at = Column(DateTime, default=datetime.utcnow)
    
    owner = relationship("User", back_populates="accounts")
    data_records = relationship("DataRecord", back_populates="account")

class DataRecord(Base):
    __tablename__ = "data_records"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    data_type = Column(String(50))  # user_summary, article_summary, etc.
    date = Column(DateTime)
    data = Column(JSON)  # 存储JSON格式的数据
    created_at = Column(DateTime, default=datetime.utcnow)

    account = relationship("PlatformAccount", back_populates="data_records")