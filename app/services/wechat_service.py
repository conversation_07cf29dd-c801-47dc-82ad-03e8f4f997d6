import asyncio
import base64
import json
import re
import os
from datetime import datetime, timed<PERSON>ta
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rows<PERSON>, BrowserContext
from typing import Optional, Dict, Any, List

class WeChatMPService:
    def __init__(self, account_id: Optional[int] = None):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.account_id = account_id
        self.user_data_dir = self._get_user_data_dir()
    
    def _get_user_data_dir(self) -> str:
        """获取用户数据目录路径"""
        if self.account_id:
            # 为每个账号创建独立的用户数据目录
            base_dir = os.path.join(os.getcwd(), "user_data")
            user_dir = os.path.join(base_dir, f"wechat_account_{self.account_id}")
        else:
            # 默认目录
            user_dir = os.path.join(os.getcwd(), "user_data", "default")
        
        # 确保目录存在
        os.makedirs(user_dir, exist_ok=True)
        return user_dir
    
    async def _create_persistent_context(self) -> BrowserContext:
        """创建持久化的浏览器上下文"""
        if not self.browser:
            raise Exception("浏览器未初始化")
        
        # 使用持久化上下文
        self.context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport={"width": 1920, "height": 1080},
            # 这里可以添加更多配置
        )
        
        return self.context
    
    async def save_login_state(self, file_path: Optional[str] = None) -> bool:
        """保存登录状态到文件"""
        try:
            if not self.context:
                print("没有可用的浏览器上下文")
                return False
            
            if not file_path:
                file_path = os.path.join(self.user_data_dir, "login_state.json")
            
            # 获取cookies
            cookies = await self.context.cookies()
            
            # 获取localStorage
            storage_state = await self.context.storage_state()
            
            # 保存状态
            login_state = {
                "cookies": cookies,
                "storage_state": storage_state,
                "saved_at": datetime.now().isoformat(),
                "account_id": self.account_id
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(login_state, f, ensure_ascii=False, indent=2)
            
            print(f"登录状态已保存到: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存登录状态失败: {e}")
            return False
    
    async def load_login_state(self, file_path: Optional[str] = None) -> bool:
        """从文件加载登录状态"""
        try:
            if not file_path:
                file_path = os.path.join(self.user_data_dir, "login_state.json")
            
            if not os.path.exists(file_path):
                print(f"登录状态文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                login_state = json.load(f)
            
            # 检查状态是否过期（比如7天）
            saved_at = datetime.fromisoformat(login_state.get("saved_at", ""))
            if datetime.now() - saved_at > timedelta(days=7):
                print("登录状态已过期")
                os.remove(file_path)  # 删除过期的状态文件
                return False
            
            # 如果还没有创建浏览器，先创建
            if not self.browser:
                await self._init_browser()
            
            # 使用保存的状态创建上下文
            storage_state = login_state.get("storage_state")
            if storage_state:
                self.context = await self.browser.new_context(
                    user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    viewport={"width": 1920, "height": 1080},
                    storage_state=storage_state
                )
            else:
                # 只恢复cookies的情况
                self.context = await self._create_persistent_context()
                # 添加cookies
                cookies = login_state.get("cookies", [])
                if cookies:
                    await self.context.add_cookies(cookies)
            
            # 创建页面
            self.page = await self.context.new_page()
            
            print(f"登录状态已从 {file_path} 恢复")
            return True
            
        except Exception as e:
            print(f"加载登录状态失败: {e}")
            return False
    
    async def _init_browser(self):
        """初始化浏览器"""
        if self.playwright:
            await self.playwright.stop()
            
        self.playwright = await async_playwright().start()
        
        # 配置浏览器启动参数
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        )
    
    async def get_login_qrcode(self) -> Optional[str]:
        """获取微信公众号登录二维码"""
        try:
            # 首先尝试加载已保存的登录状态
            if await self.load_login_state():
                # 检查是否已经登录
                if await self.check_existing_login():
                    print("检测到已有有效的登录状态")
                    return "already_logged_in"
            
            # 如果没有有效的登录状态，清理并重新开始
            await self.close()
            
            # 初始化浏览器
            await self._init_browser()
            
            # 创建新的上下文
            self.context = await self._create_persistent_context()
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            print("正在访问微信公众号登录页面...")
            # 访问微信公众号登录页面，增加超时时间
            await self.page.goto("https://mp.weixin.qq.com", wait_until="networkidle", timeout=30000)
            
            # 等待页面完全加载
            await asyncio.sleep(2)
            
            # 尝试多个可能的二维码选择器
            qr_selectors = [
                ".login__type__container__scan__qrcode",
                ".qrcode",
                ".login_qrcode",
                ".weui-desktop-login__qrcode",
                "img[src*='qr']",
                ".login-qrcode img"
            ]
            
            qr_element = None
            for selector in qr_selectors:
                try:
                    print(f"尝试选择器: {selector}")
                    await self.page.wait_for_selector(selector, state="visible", timeout=10000)
                    qr_element = await self.page.query_selector(selector)
                    if qr_element:
                        print(f"找到二维码元素: {selector}")
                        break
                except Exception as e:
                    print(f"选择器 {selector} 失败: {e}")
                    continue
            
            if not qr_element:
                # 如果找不到二维码，尝试截取整个页面查看问题
                page_screenshot = await self.page.screenshot()
                page_base64 = base64.b64encode(page_screenshot).decode()
                print("未找到二维码元素，返回整个页面截图用于调试")
                return f"data:image/png;base64,{page_base64}"
            
            # 等待二维码图片完全加载
            await asyncio.sleep(1)
            
            # 检查元素是否可见
            is_visible = await qr_element.is_visible()
            if not is_visible:
                print("二维码元素不可见")
                return None
            
            # 获取二维码图片
            qr_screenshot = await qr_element.screenshot()
            
            # 检查截图是否为空
            if len(qr_screenshot) == 0:
                print("二维码截图为空")
                return None
            
            # 转换为base64
            qr_base64 = base64.b64encode(qr_screenshot).decode()
            print(f"成功获取二维码，大小: {len(qr_screenshot)} bytes")
            
            return f"data:image/png;base64,{qr_base64}"
            
        except Exception as e:
            print(f"获取二维码失败: {e}")
            # 在出错时也尝试清理资源
            await self.close()
            return None
    
    async def check_existing_login(self) -> bool:
        """检查现有登录状态是否仍然有效"""
        try:
            if not self.page:
                return False
            
            # 访问微信公众号首页
            await self.page.goto("https://mp.weixin.qq.com", wait_until="networkidle", timeout=15000)
            await asyncio.sleep(2)
            
            # 检查是否已经登录（URL包含token）
            current_url = self.page.url
            if "token=" in current_url:
                print("检测到有效的登录状态")
                return True
            else:
                print("登录状态已失效")
                return False
                
        except Exception as e:
            print(f"检查现有登录状态失败: {e}")
            return False
    
    async def logout(self, clear_saved_state: bool = True) -> bool:
        """注销登录
        
        Args:
            clear_saved_state: 是否清除保存的登录状态文件
            
        Returns:
            是否成功注销
        """
        try:
            print("开始注销登录...")
            
            # 1. 尝试从微信公众号后台注销
            logout_success = await self._logout_from_wechat()
            
            # 2. 清除浏览器会话
            await self._clear_browser_session()
            
            # 3. 清除保存的登录状态文件
            if clear_saved_state:
                self._clear_saved_login_state()
            
            print("✅ 注销登录完成")
            return logout_success
            
        except Exception as e:
            print(f"注销登录过程中发生错误: {e}")
            # 即使出错也要清理本地状态
            try:
                await self._clear_browser_session()
                if clear_saved_state:
                    self._clear_saved_login_state()
            except:
                pass
            return False
    
    async def _logout_from_wechat(self) -> bool:
        """从微信公众号后台注销"""
        try:
            if not self.page:
                print("页面未初始化，跳过微信后台注销")
                return True
            
            # 检查当前是否已登录
            current_url = self.page.url
            if "token=" not in current_url:
                print("当前未登录，无需从微信后台注销")
                return True
            
            print("尝试从微信公众号后台注销...")
            
            # 提取当前token
            token = self._extract_token_from_url()
            
            # 方法1: 尝试访问正确的注销URL（基于你观察到的实际URL）
            try:
                if token:
                    # 使用正确的注销URL格式
                    logout_url = f"https://mp.weixin.qq.com/cgi-bin/logout?t=wxm-logout&token={token}&lang=zh_CN"
                    print(f"使用注销URL: {logout_url}")
                else:
                    # 如果无法提取token，使用通用的注销URL
                    logout_url = "https://mp.weixin.qq.com/cgi-bin/logout?t=wxm-logout&lang=zh_CN"
                    print(f"使用通用注销URL: {logout_url}")
                
                await self.page.goto(logout_url, wait_until="networkidle", timeout=10000)
                await asyncio.sleep(2)
                
                # 检查是否已经注销（URL不再包含token或跳转到登录页）
                new_url = self.page.url
                if "token=" not in new_url or "mp.weixin.qq.com" in new_url and new_url.find("/") == new_url.rfind("/"):
                    print("✅ 通过注销URL成功注销")
                    return True
                    
            except Exception as e:
                print(f"注销URL方法失败: {e}")
            
            # 方法2: 尝试点击页面上的注销按钮/链接
            try:
                # 常见的注销元素选择器（基于实际观察更新）
                logout_selectors = [
                    "a[href*='cgi-bin/logout']",  # 基于你观察到的URL格式
                    "a[href*='loginquit']",       # 原有的格式
                    "a[href*='logout']", 
                    ".logout",
                    ".quit",
                    "[data-action='logout']",
                    "a:contains('退出')",
                    "a:contains('注销')",
                    ".js_quit",
                    ".header_tool_logout",        # 可能的页面头部注销按钮
                    ".tool_logout"                # 可能的工具栏注销按钮
                ]
                
                for selector in logout_selectors:
                    try:
                        logout_element = await self.page.query_selector(selector)
                        if logout_element and await logout_element.is_visible():
                            print(f"找到注销元素: {selector}")
                            await logout_element.click()
                            await asyncio.sleep(3)
                            
                            # 检查是否成功注销
                            new_url = self.page.url
                            if "token=" not in new_url:
                                print("✅ 通过点击注销元素成功注销")
                                return True
                            break
                    except Exception as inner_e:
                        continue
                        
            except Exception as e:
                print(f"点击注销元素方法失败: {e}")
            
            # 方法3: 清除所有cookies（强制注销）
            try:
                print("尝试通过清除cookies强制注销...")
                if self.context:
                    # 清除所有cookies
                    await self.context.clear_cookies()
                    
                    # 刷新页面验证
                    await self.page.reload(wait_until="networkidle", timeout=10000)
                    await asyncio.sleep(2)
                    
                    new_url = self.page.url
                    if "token=" not in new_url:
                        print("✅ 通过清除cookies成功注销")
                        return True
                        
            except Exception as e:
                print(f"清除cookies方法失败: {e}")
            
            print("⚠️ 微信后台注销可能未完全成功，但将继续清理本地状态")
            return False
            
        except Exception as e:
            print(f"从微信后台注销失败: {e}")
            return False
    
    async def _clear_browser_session(self):
        """清除浏览器会话"""
        try:
            print("清除浏览器会话...")
            
            # 清除当前上下文的所有数据
            if self.context:
                try:
                    # 清除cookies
                    await self.context.clear_cookies()
                    
                    # 清除localStorage和sessionStorage
                    if self.page:
                        await self.page.evaluate("""
                            () => {
                                try {
                                    localStorage.clear();
                                    sessionStorage.clear();
                                } catch (e) {
                                    console.log('清除存储失败:', e);
                                }
                            }
                        """)
                    
                    print("✅ 浏览器会话数据已清除")
                    
                except Exception as e:
                    print(f"清除浏览器会话数据失败: {e}")
            
            # 关闭当前页面和上下文
            await self.close()
            
        except Exception as e:
            print(f"清除浏览器会话失败: {e}")
    
    def _clear_saved_login_state(self):
        """清除保存的登录状态文件"""
        try:
            state_file = os.path.join(self.user_data_dir, "login_state.json")
            
            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"✅ 已删除保存的登录状态文件: {state_file}")
            else:
                print("没有找到需要删除的登录状态文件")
                
            # 如果整个账号目录为空，也删除目录
            try:
                if os.path.exists(self.user_data_dir) and not os.listdir(self.user_data_dir):
                    os.rmdir(self.user_data_dir)
                    print(f"✅ 已删除空的用户数据目录: {self.user_data_dir}")
            except:
                pass  # 目录不为空或其他原因，忽略
                
        except Exception as e:
            print(f"清除保存的登录状态文件失败: {e}")
    
    async def force_logout(self) -> bool:
        """强制注销（忽略所有错误，确保本地状态被清理）"""
        try:
            print("开始强制注销...")
            
            # 直接清理所有状态，不管是否成功
            tasks = []
            
            # 异步执行清理任务
            if self.context:
                tasks.append(self._clear_browser_session())
            
            # 等待所有清理任务完成（有超时保护）
            if tasks:
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*tasks, return_exceptions=True), 
                        timeout=10.0
                    )
                except asyncio.TimeoutError:
                    print("⚠️ 清理任务超时，强制继续")
            
            # 清除保存的状态文件
            self._clear_saved_login_state()
            
            # 重置所有实例变量
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            
            print("✅ 强制注销完成")
            return True
            
        except Exception as e:
            print(f"强制注销过程中发生错误: {e}")
            # 即使出错也要尝试清理基本状态
            try:
                self._clear_saved_login_state()
                self.page = None
                self.context = None
                self.browser = None
                self.playwright = None
            except:
                pass
            return True  # 强制注销总是返回成功
    
    async def refresh_qrcode(self) -> Optional[str]:
        """刷新二维码（当二维码过期时使用）"""
        try:
            if not self.page:
                print("页面未初始化，重新获取二维码")
                return await self.get_login_qrcode()
            
            print("正在刷新二维码...")
            # 刷新页面
            await self.page.reload(wait_until="networkidle", timeout=30000)
            await asyncio.sleep(2)
            
            # 尝试多个可能的二维码选择器
            qr_selectors = [
                ".login__type__container__scan__qrcode",
                ".qrcode",
                ".login_qrcode",
                ".weui-desktop-login__qrcode",
                "img[src*='qr']",
                ".login-qrcode img"
            ]
            
            qr_element = None
            for selector in qr_selectors:
                try:
                    await self.page.wait_for_selector(selector, state="visible", timeout=10000)
                    qr_element = await self.page.query_selector(selector)
                    if qr_element:
                        print(f"刷新后找到二维码元素: {selector}")
                        break
                except Exception as e:
                    continue
            
            if not qr_element:
                print("刷新后仍未找到二维码元素")
                return None
            
            # 等待二维码图片完全加载
            await asyncio.sleep(1)
            
            # 获取二维码图片
            qr_screenshot = await qr_element.screenshot()
            
            if len(qr_screenshot) == 0:
                print("刷新后二维码截图为空")
                return None
            
            # 转换为base64
            qr_base64 = base64.b64encode(qr_screenshot).decode()
            print(f"成功刷新二维码，大小: {len(qr_screenshot)} bytes")
            
            return f"data:image/png;base64,{qr_base64}"
            
        except Exception as e:
            print(f"刷新二维码失败: {e}")
            return None

    async def check_login_status(self, wait_for_redirect: bool = True, timeout: int = 30) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                print("页面未初始化")
                return False
            
            # 获取当前URL
            current_url = self.page.url
            print(f"当前URL: {current_url}")
            
            # 如果已经有token，直接返回登录成功
            if "mp.weixin.qq.com" in current_url and "token=" in current_url:
                print("检测到已登录状态")
                return True
            
            # 检查是否还在登录页面
            if "mp.weixin.qq.com" in current_url and "token=" not in current_url:
                # 检查是否已经扫码成功但页面还没跳转
                success_indicators = [
                    # 扫码成功的提示文字或元素
                    ".login__type__container__scan__desc--success",
                    ".scan-success",
                    ".login-success",
                    "[class*='success']",
                    # 可能的跳转提示
                    ".redirecting",
                    ".loading"
                ]
                
                login_success_detected = False
                for selector in success_indicators:
                    try:
                        success_element = await self.page.query_selector(selector)
                        if success_element and await success_element.is_visible():
                            print(f"检测到登录成功指示器: {selector}")
                            login_success_detected = True
                            break
                    except:
                        continue
                
                # 检查页面内容变化（比如二维码消失，出现用户头像等）
                if not login_success_detected:
                    try:
                        # 检查二维码是否消失或被替换
                        qr_element = await self.page.query_selector(".login__type__container__scan__qrcode")
                        if not qr_element or not await qr_element.is_visible():
                            print("二维码已消失，可能已扫码成功")
                            login_success_detected = True
                        
                        # 检查是否出现用户头像或其他登录后的元素
                        avatar_selectors = [
                            ".user-avatar",
                            ".avatar", 
                            ".user-info",
                            "[class*='avatar']",
                            "img[src*='avatar']"
                        ]
                        
                        for selector in avatar_selectors:
                            try:
                                avatar_element = await self.page.query_selector(selector)
                                if avatar_element and await avatar_element.is_visible():
                                    print(f"检测到用户头像: {selector}")
                                    login_success_detected = True
                                    break
                            except:
                                continue
                                
                    except Exception as inner_e:
                        print(f"检查页面元素变化失败: {inner_e}")
                
                # 如果检测到登录成功但还没跳转，等待页面跳转
                if login_success_detected and wait_for_redirect:
                    print(f"检测到登录成功，等待页面跳转（最多{timeout}秒）...")
                    
                    try:
                        # 等待URL变化（包含token）或页面跳转
                        await self.page.wait_for_function(
                            "() => window.location.href.includes('token=')",
                            timeout=timeout * 1000
                        )
                        
                        new_url = self.page.url
                        print(f"页面已跳转到: {new_url}")
                        
                        if "token=" in new_url:
                            print("登录成功并完成跳转")
                            return True
                            
                    except Exception as wait_e:
                        print(f"等待页面跳转超时: {wait_e}")
                        
                        # 尝试手动刷新页面
                        print("尝试刷新页面触发跳转...")
                        try:
                            await self.page.reload(wait_until="networkidle", timeout=10000)
                            await asyncio.sleep(2)
                            
                            new_url = self.page.url
                            if "token=" in new_url:
                                print("刷新后检测到登录状态")
                                return True
                        except:
                            print("刷新页面失败")
                
                # 检查二维码是否过期
                try:
                    expired_selectors = [
                        ".login__type__container__scan__desc--fail",
                        ".qrcode-expired",
                        ".login-qrcode-expired",
                        "[class*='expired']",
                        "[class*='fail']"
                    ]
                    
                    for selector in expired_selectors:
                        expired_element = await self.page.query_selector(selector)
                        if expired_element and await expired_element.is_visible():
                            print("检测到二维码已过期")
                            return False
                            
                except Exception as inner_e:
                    print(f"检查二维码过期状态失败: {inner_e}")
                
                if login_success_detected:
                    print("检测到登录成功，但页面跳转可能延迟")
                    # 可能已经登录成功，但页面跳转有延迟
                    return True
                else:
                    print("仍在等待扫码登录")
                    return False
            
            print("页面状态异常")
            return False
                
        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False
    
    async def wait_for_login_complete(self, max_wait_time: int = 300) -> bool:
        """等待用户完成扫码登录，返回是否登录成功"""
        try:
            if not self.page:
                print("页面未初始化")
                return False
            
            print(f"开始等待用户扫码登录（最多等待{max_wait_time}秒）...")
            
            start_time = asyncio.get_event_loop().time()
            check_interval = 2  # 每2秒检查一次
            
            while True:
                current_time = asyncio.get_event_loop().time()
                elapsed_time = current_time - start_time
                
                if elapsed_time > max_wait_time:
                    print("等待登录超时")
                    return False
                
                # 检查登录状态
                login_status = await self.check_login_status(wait_for_redirect=True, timeout=10)
                
                if login_status:
                    print("用户已成功登录")
                    # 保存登录状态
                    await self.save_login_state()
                    return True
                
                # 检查二维码是否过期，如果过期则自动刷新
                try:
                    expired_selectors = [
                        ".login__type__container__scan__desc--fail",
                        ".qrcode-expired", 
                        ".login-qrcode-expired",
                        "[class*='expired']",
                        "[class*='fail']"
                    ]
                    
                    qr_expired = False
                    for selector in expired_selectors:
                        expired_element = await self.page.query_selector(selector)
                        if expired_element and await expired_element.is_visible():
                            print("检测到二维码已过期，自动刷新...")
                            qr_expired = True
                            break
                    
                    if qr_expired:
                        # 刷新二维码
                        new_qr = await self.refresh_qrcode()
                        if new_qr:
                            print("二维码已刷新，请重新扫码")
                        else:
                            print("刷新二维码失败")
                            return False
                            
                except Exception as check_e:
                    print(f"检查二维码状态失败: {check_e}")
                
                # 显示剩余时间
                remaining_time = max_wait_time - elapsed_time
                print(f"等待扫码中... 剩余时间: {remaining_time:.0f}秒")
                
                await asyncio.sleep(check_interval)
                
        except Exception as e:
            print(f"等待登录过程中发生错误: {e}")
            return False

    async def download_data_excel(self, begin_date: str, end_date: str, busi: int = 3, tmpl: int = 19) -> Optional[bytes]:
        """下载微信公众号数据Excel文件
        
        Args:
            begin_date: 开始日期，格式: YYYYMMDD 或 YYYY-MM-DD
            end_date: 结束日期，格式: YYYYMMDD 或 YYYY-MM-DD  
            busi: 业务类型，默认3
            tmpl: 模板类型，默认19
            
        Returns:
            Excel文件的二进制数据，失败返回None
        """
        try:
            if not self.page:
                print("页面未初始化，无法下载数据")
                return None
            
            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            print(f"当前URL: {current_url}")
            
            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                # 导航到微信公众号主页，会自动跳转到登录后的页面
                try:
                    await self.page.goto("https://mp.weixin.qq.com", 
                                       wait_until="networkidle", timeout=15000)
                    await asyncio.sleep(3)  # 等待自动跳转完成
                    
                    # 更新当前URL
                    current_url = self.page.url
                    print(f"导航后的URL: {current_url}")
                    
                    # 再次检查是否有token
                    if "token=" not in current_url:
                        print("导航后仍未检测到登录状态，可能登录已过期")
                        return None
                        
                except Exception as nav_e:
                    print(f"导航到微信公众号页面失败: {nav_e}")
                    return None
            
            # 提取token
            token = self._extract_token_from_url()
            if not token:
                print("无法获取token，无法下载数据")
                return None
            
            # 格式化日期
            formatted_begin_date = self._format_date_for_download(begin_date)
            formatted_end_date = self._format_date_for_download(end_date)
            
            if not formatted_begin_date or not formatted_end_date:
                print("日期格式错误")
                return None
            
            print(f"开始下载数据: {formatted_begin_date} 到 {formatted_end_date}")
            
            # 构建下载URL
            download_url = f"https://mp.weixin.qq.com/misc/datacubequery?action=query_download&busi={busi}&tmpl={tmpl}&args={{\"begin_date\":{formatted_begin_date},\"end_date\":{formatted_end_date}}}&token={token}&lang=zh_CN"
            
            print(f"下载URL: {download_url}")
            
            # 直接使用HTTP请求下载（原备用方法改为首选）
            try:
                # 获取当前页面的cookies
                cookies = await self.context.cookies()
                
                # 构建cookie字符串
                cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
                
                # 使用页面的request方法发送请求
                response = await self.page.request.get(
                    download_url,
                    headers={
                        'Cookie': cookie_str,
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Referer': 'https://mp.weixin.qq.com/'
                    }
                )
                
                if response.status == 200:
                    file_content = await response.body()
                    print(f"数据下载成功，文件大小: {len(file_content)} bytes")
                    return file_content
                else:
                    print(f"下载失败，状态码: {response.status}")
                    # 尝试获取错误信息
                    error_text = await response.text()
                    print(f"错误响应: {error_text}")
                    return None
                    
            except Exception as request_e:
                print(f"HTTP请求下载失败: {request_e}")
                # 如果HTTP请求失败，尝试页面下载方法
                return await self._download_data_fallback_page_method(begin_date, end_date, busi, tmpl)
                    
        except Exception as e:
            print(f"下载数据失败: {e}")
            return None
    
    async def _download_data_fallback_page_method(self, begin_date: str, end_date: str, busi: int, tmpl: int) -> Optional[bytes]:
        """备用下载方法：使用页面下载事件"""
        try:
            print("尝试页面下载方法...")
            
            # 格式化日期
            formatted_begin_date = self._format_date_for_download(begin_date)
            formatted_end_date = self._format_date_for_download(end_date)
            
            token = self._extract_token_from_url()
            
            # 构建下载URL
            download_url = f"https://mp.weixin.qq.com/misc/datacubequery?action=query_download&busi={busi}&tmpl={tmpl}&args={{\"begin_date\":{formatted_begin_date},\"end_date\":{formatted_end_date}}}&token={token}&lang=zh_CN"
            
            # 使用页面来监听下载事件
            async with self.page.expect_download() as download_info:
                # 触发下载
                await self.page.goto(download_url)
                
                # 等待下载完成
                download = await download_info.value
                
                # 读取下载的文件内容
                file_path = await download.path()
                if file_path:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()
                    
                    print(f"页面方法下载成功，文件大小: {len(file_content)} bytes")
                    return file_content
                else:
                    print("页面方法下载文件路径为空")
                    return None
                    
        except Exception as e:
            print(f"页面下载方法失败: {e}")
            return None
    
    async def _download_data_fallback(self, begin_date: str, end_date: str, busi: int, tmpl: int) -> Optional[bytes]:
        """备用下载方法：直接发送HTTP请求"""
        try:
            # 获取当前页面的cookies
            cookies = await self.context.cookies()
            
            # 构建cookie字符串
            cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            
            # 格式化日期
            formatted_begin_date = self._format_date_for_download(begin_date)
            formatted_end_date = self._format_date_for_download(end_date)
            
            token = self._extract_token_from_url()
            
            # 构建下载URL
            download_url = f"https://mp.weixin.qq.com/misc/datacubequery?action=query_download&busi={busi}&tmpl={tmpl}&args={{\"begin_date\":{formatted_begin_date},\"end_date\":{formatted_end_date}}}&token={token}&lang=zh_CN"
            
            # 使用页面的request方法发送请求
            response = await self.page.request.get(
                download_url,
                headers={
                    'Cookie': cookie_str,
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://mp.weixin.qq.com/'
                }
            )
            
            if response.status == 200:
                file_content = await response.body()
                print(f"备用方法下载成功，文件大小: {len(file_content)} bytes")
                return file_content
            else:
                print(f"备用下载失败，状态码: {response.status}")
                return None
                
        except Exception as e:
            print(f"备用下载方法失败: {e}")
            return None
    
    def _format_date_for_download(self, date_str: str) -> Optional[str]:
        """格式化日期为下载所需的格式 YYYYMMDD"""
        try:
            # 去除可能的空格
            date_str = date_str.strip()
            
            # 如果已经是8位数字格式
            if len(date_str) == 8 and date_str.isdigit():
                return date_str
            
            # 如果是带分隔符的格式，尝试解析
            if '-' in date_str:
                parts = date_str.split('-')
            elif '/' in date_str:
                parts = date_str.split('/')
            else:
                return None
                
            if len(parts) == 3:
                year, month, day = parts
                # 确保年份是4位，月份和日期是1-2位数字
                if len(year) == 4 and year.isdigit() and month.isdigit() and day.isdigit():
                    return f"{year}{month.zfill(2)}{day.zfill(2)}"
            
            return None
            
        except Exception as e:
            print(f"日期格式化失败: {e}")
            return None

    async def get_cookies(self) -> Optional[str]:
        """获取登录后的cookies"""
        try:
            if not self.context:
                return None
                
            cookies = await self.context.cookies()
            return str(cookies)
            
        except Exception as e:
            print(f"获取cookies失败: {e}")
            return None
    
    async def get_user_summary_data(self, start_date: str, end_date: str) -> Optional[Dict[str, Any]]:
        """获取用户分析数据"""
        try:
            if not self.page:
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                await self.page.goto("https://mp.weixin.qq.com", 
                                   wait_until="networkidle", timeout=15000)
                await asyncio.sleep(3)  # 等待自动跳转完成

            # 导航到用户分析页面
            await self.page.goto("https://mp.weixin.qq.com/misc/appmsganalysis?action=report&token=" +
                                self._extract_token_from_url() + "&lang=zh_CN")

            # 等待页面加载
            await self.page.wait_for_selector("#main", timeout=10000)

            # 设置日期范围
            await self._set_date_range(start_date, end_date)

            # 等待数据加载
            await asyncio.sleep(3)

            # 提取用户数据
            user_data = await self._extract_user_data()

            return user_data

        except Exception as e:
            print(f"获取用户数据失败: {e}")
            return None

    async def get_article_summary_data(self, start_date: str, end_date: str) -> Optional[List[Dict[str, Any]]]:
        """获取图文分析数据"""
        try:
            if not self.page:
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                await self.page.goto("https://mp.weixin.qq.com", 
                                   wait_until="networkidle", timeout=15000)
                await asyncio.sleep(3)  # 等待自动跳转完成

            # 导航到图文分析页面
            await self.page.goto("https://mp.weixin.qq.com/misc/appmsganalysis?action=appmsgstat&token=" +
                                self._extract_token_from_url())

            # 等待页面加载
            await self.page.wait_for_selector("#main", timeout=10000)

            # 设置日期范围
            await self._set_date_range(start_date, end_date)

            # 等待数据加载
            await asyncio.sleep(3)

            # 提取图文数据
            article_data = await self._extract_article_data()

            return article_data

        except Exception as e:
            print(f"获取图文数据失败: {e}")
            return None

    def _extract_token_from_url(self) -> str:
        """从当前URL中提取token"""
        if not self.page:
            return ""

        current_url = self.page.url
        token_match = re.search(r'token=([^&]+)', current_url)
        return token_match.group(1) if token_match else ""

    async def _set_date_range(self, start_date: str, end_date: str):
        """设置日期范围"""
        try:
            # 这里需要根据实际的微信公众号后台页面结构来实现
            # 由于页面结构可能变化，这里提供一个基础框架
            pass
        except Exception as e:
            print(f"设置日期范围失败: {e}")

    async def _extract_user_data(self) -> Dict[str, Any]:
        """提取用户数据"""
        try:
            # 这里需要根据实际页面结构提取数据
            # 返回示例数据结构
            return {
                "new_users": 0,
                "cancel_users": 0,
                "net_growth": 0,
                "cumulative_users": 0,
                "date": datetime.now().strftime("%Y-%m-%d")
            }
        except Exception as e:
            print(f"提取用户数据失败: {e}")
            return {}

    async def _extract_article_data(self) -> List[Dict[str, Any]]:
        """提取图文数据"""
        try:
            # 这里需要根据实际页面结构提取数据
            # 返回示例数据结构
            return [
                {
                    "title": "示例文章",
                    "read_count": 0,
                    "like_count": 0,
                    "share_count": 0,
                    "publish_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]
        except Exception as e:
            print(f"提取图文数据失败: {e}")
            return []

    async def close(self):
        """关闭浏览器和清理资源"""
        try:
            if self.page:
                print("关闭页面...")
                await self.page.close()
                self.page = None
        except Exception as e:
            print(f"关闭页面失败: {e}")
            
        try:
            if self.context:
                print("关闭浏览器上下文...")
                await self.context.close()
                self.context = None
        except Exception as e:
            print(f"关闭浏览器上下文失败: {e}")
            
        try:
            if self.browser:
                print("关闭浏览器...")
                await self.browser.close()
                self.browser = None
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
            
        try:
            if self.playwright:
                print("停止Playwright...")
                await self.playwright.stop()
                self.playwright = None
        except Exception as e:
            print(f"停止Playwright失败: {e}")
        
        print("资源清理完成")