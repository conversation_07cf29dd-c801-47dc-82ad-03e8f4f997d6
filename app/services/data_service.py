from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.models import DataRecord, PlatformAccount
import json

class DataService:
    """数据分析服务"""
    
    @staticmethod
    def get_dashboard_summary(db: Session, user_id: int) -> Dict[str, Any]:
        """获取Dashboard概览数据"""
        
        # 获取用户的所有账号
        accounts = db.query(PlatformAccount).filter(PlatformAccount.user_id == user_id).all()
        
        if not accounts:
            return {
                "total_accounts": 0,
                "logged_in_accounts": 0,
                "total_data_records": 0,
                "recent_data": []
            }
        
        account_ids = [account.id for account in accounts]
        
        # 统计基本信息
        total_accounts = len(accounts)
        logged_in_accounts = sum(1 for account in accounts if account.login_status)
        
        # 获取数据记录总数
        total_data_records = db.query(DataRecord).filter(
            DataRecord.account_id.in_(account_ids)
        ).count()
        
        # 获取最近的数据记录
        recent_data = db.query(DataRecord).filter(
            DataRecord.account_id.in_(account_ids)
        ).order_by(DataRecord.created_at.desc()).limit(10).all()
        
        return {
            "total_accounts": total_accounts,
            "logged_in_accounts": logged_in_accounts,
            "total_data_records": total_data_records,
            "recent_data": [
                {
                    "id": record.id,
                    "account_name": next((acc.name for acc in accounts if acc.id == record.account_id), "未知账号"),
                    "data_type": record.data_type,
                    "date": record.date.strftime("%Y-%m-%d"),
                    "created_at": record.created_at
                }
                for record in recent_data
            ]
        }
    
    @staticmethod
    def get_user_growth_trend(db: Session, account_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """获取用户增长趋势"""
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询用户数据记录
        records = db.query(DataRecord).filter(
            DataRecord.account_id == account_id,
            DataRecord.data_type == "user_summary",
            DataRecord.date >= start_date,
            DataRecord.date <= end_date
        ).order_by(DataRecord.date).all()
        
        trend_data = []
        for record in records:
            data = record.data
            if isinstance(data, str):
                data = json.loads(data)
            
            trend_data.append({
                "date": record.date.strftime("%Y-%m-%d"),
                "new_users": data.get("new_users", 0),
                "cancel_users": data.get("cancel_users", 0),
                "net_growth": data.get("net_growth", 0),
                "cumulative_users": data.get("cumulative_users", 0)
            })
        
        return trend_data
    
    @staticmethod
    def get_article_performance(db: Session, account_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """获取文章表现数据"""
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询图文数据记录
        records = db.query(DataRecord).filter(
            DataRecord.account_id == account_id,
            DataRecord.data_type == "article_summary",
            DataRecord.date >= start_date,
            DataRecord.date <= end_date
        ).order_by(DataRecord.date.desc()).all()
        
        article_data = []
        for record in records:
            data = record.data
            if isinstance(data, str):
                data = json.loads(data)
            
            # 如果data是列表（多篇文章）
            if isinstance(data, list):
                for article in data:
                    article_data.append({
                        "date": record.date.strftime("%Y-%m-%d"),
                        "title": article.get("title", ""),
                        "read_count": article.get("read_count", 0),
                        "like_count": article.get("like_count", 0),
                        "share_count": article.get("share_count", 0),
                        "publish_time": article.get("publish_time", "")
                    })
        
        return article_data
    
    @staticmethod
    def get_platform_statistics(db: Session, user_id: int) -> Dict[str, Any]:
        """获取平台统计数据"""
        
        # 获取用户的所有账号
        accounts = db.query(PlatformAccount).filter(PlatformAccount.user_id == user_id).all()
        
        # 按平台分组统计
        platform_stats = {}
        for account in accounts:
            platform = account.platform
            if platform not in platform_stats:
                platform_stats[platform] = {
                    "total_accounts": 0,
                    "logged_in_accounts": 0,
                    "data_records": 0
                }
            
            platform_stats[platform]["total_accounts"] += 1
            if account.login_status:
                platform_stats[platform]["logged_in_accounts"] += 1
            
            # 统计数据记录数
            data_count = db.query(DataRecord).filter(DataRecord.account_id == account.id).count()
            platform_stats[platform]["data_records"] += data_count
        
        return platform_stats
    
    @staticmethod
    def calculate_engagement_rate(read_count: int, like_count: int, share_count: int) -> float:
        """计算互动率"""
        if read_count == 0:
            return 0.0
        
        engagement = like_count + share_count
        return round((engagement / read_count) * 100, 2)
    
    @staticmethod
    def get_top_articles(db: Session, account_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门文章"""
        
        # 查询最近的图文数据
        records = db.query(DataRecord).filter(
            DataRecord.account_id == account_id,
            DataRecord.data_type == "article_summary"
        ).order_by(DataRecord.date.desc()).limit(50).all()
        
        all_articles = []
        for record in records:
            data = record.data
            if isinstance(data, str):
                data = json.loads(data)
            
            if isinstance(data, list):
                for article in data:
                    article_info = {
                        "title": article.get("title", ""),
                        "read_count": article.get("read_count", 0),
                        "like_count": article.get("like_count", 0),
                        "share_count": article.get("share_count", 0),
                        "publish_time": article.get("publish_time", ""),
                        "date": record.date.strftime("%Y-%m-%d")
                    }
                    article_info["engagement_rate"] = DataService.calculate_engagement_rate(
                        article_info["read_count"],
                        article_info["like_count"],
                        article_info["share_count"]
                    )
                    all_articles.append(article_info)
        
        # 按阅读量排序，取前N篇
        top_articles = sorted(all_articles, key=lambda x: x["read_count"], reverse=True)[:limit]
        
        return top_articles
