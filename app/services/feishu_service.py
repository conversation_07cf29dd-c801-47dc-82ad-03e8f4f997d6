import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from lark_oapi import Client
from lark_oapi.api.bitable.v1 import *
from lark_oapi.core.const import FEISHU_DOMAIN

logger = logging.getLogger(__name__)

class FeishuService:
    """飞书多维表格服务类"""
    
    def __init__(self, app_id: Optional[str] = None, app_secret: Optional[str] = None):
        """初始化飞书客户端
        
        Args:
            app_id: 飞书应用ID，如果不提供则从环境变量获取
            app_secret: 飞书应用密钥，如果不提供则从环境变量获取
        """
        self.app_id = app_id or os.getenv("FEISHU_APP_ID")
        self.app_secret = app_secret or os.getenv("FEISHU_APP_SECRET")
        
        if not self.app_id or not self.app_secret:
            raise ValueError("飞书应用ID和密钥不能为空，请检查环境变量FEISHU_APP_ID和FEISHU_APP_SECRET")
        
        # 构建客户端
        self.client = Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .domain(FEISHU_DOMAIN) \
            .log_level(logging.INFO) \
            .build()
    
    async def create_bitable(self, name: str, folder_token: Optional[str] = None) -> Optional[str]:
        """创建多维表格
        
        Args:
            name: 表格名称
            folder_token: 存放目录的token，可选
            
        Returns:
            创建成功返回app_token，失败返回None
        """
        try:
            # 构建请求
            request = CreateAppRequest.builder() \
                .request_body(CreateAppRequestBody.builder()
                    .name(name)
                    .folder_token(folder_token)
                    .build()) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.app.create(request)
            
            if not response.success():
                logger.error(f"创建多维表格失败: {response.code}, {response.msg}")
                return None
            
            app_token = response.data.app.app_token
            logger.info(f"成功创建多维表格: {name}, app_token: {app_token}")
            return app_token
            
        except Exception as e:
            logger.error(f"创建多维表格异常: {e}")
            return None
    
    async def create_table(self, app_token: str, table_name: str, fields: Optional[List[Dict]] = None) -> Optional[str]:
        """在多维表格中创建数据表
        
        Args:
            app_token: 多维表格的app_token
            table_name: 数据表名称
            fields: 字段定义列表，可选
            
        Returns:
            创建成功返回table_id，失败返回None
        """
        try:
            # 构建请求体
            request_body = CreateTableRequest.builder() \
                .request_body(CreateTableRequestBody.builder()
                    .table(AppTableCreateHeader.builder()
                        .name(table_name)
                        .build())
                    .build()) \
                .app_token(app_token) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.table.create(request_body)
            
            if not response.success():
                logger.error(f"创建数据表失败: {response.code}, {response.msg}")
                return None
            
            table_id = response.data.table_id
            logger.info(f"成功创建数据表: {table_name}, table_id: {table_id}")
            return table_id
            
        except Exception as e:
            logger.error(f"创建数据表异常: {e}")
            return None
    
    async def batch_create_records(self, app_token: str, table_id: str, records: List[Dict[str, Any]]) -> Optional[List[str]]:
        """批量创建记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            records: 记录数据列表，每个记录是字段名到值的映射
            
        Returns:
            创建成功返回record_id列表，失败返回None
        """
        try:
            # 飞书API限制单次最多500条记录
            if len(records) > 500:
                logger.warning(f"记录数量({len(records)})超过500条限制，将只处理前500条")
                records = records[:500]
            
            # 构建记录数据
            app_table_records = []
            for record_data in records:
                # 将记录数据转换为飞书格式
                fields = {}
                for field_name, field_value in record_data.items():
                    fields[field_name] = field_value
                
                app_table_records.append(
                    AppTableRecord.builder()
                    .fields(fields)
                    .build()
                )
            
            # 构建请求
            request = BatchCreateTableRecordRequest.builder() \
                .request_body(BatchCreateTableRecordRequestBody.builder()
                    .records(app_table_records)
                    .build()) \
                .app_token(app_token) \
                .table_id(table_id) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.record.batch_create(request)
            
            if not response.success():
                logger.error(f"批量创建记录失败: {response.code}, {response.msg}")
                return None
            
            record_ids = [record.record_id for record in response.data.records]
            logger.info(f"成功创建{len(record_ids)}条记录")
            return record_ids
            
        except Exception as e:
            logger.error(f"批量创建记录异常: {e}")
            return None
    
    async def update_record(self, app_token: str, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """更新单条记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            record_id: 记录的record_id
            fields: 要更新的字段数据
            
        Returns:
            更新成功返回True，失败返回False
        """
        try:
            # 构建请求
            request = UpdateTableRecordRequest.builder() \
                .request_body(UpdateTableRecordRequestBody.builder()
                    .fields(fields)
                    .build()) \
                .app_token(app_token) \
                .table_id(table_id) \
                .record_id(record_id) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.record.update(request)
            
            if not response.success():
                logger.error(f"更新记录失败: {response.code}, {response.msg}")
                return False
            
            logger.info(f"成功更新记录: {record_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新记录异常: {e}")
            return False
    
    async def get_table_fields(self, app_token: str, table_id: str) -> Optional[List[Dict]]:
        """获取数据表的字段信息
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            
        Returns:
            字段信息列表，失败返回None
        """
        try:
            # 构建请求
            request = ListTableFieldRequest.builder() \
                .app_token(app_token) \
                .table_id(table_id) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.field.list(request)
            
            if not response.success():
                logger.error(f"获取表格字段失败: {response.code}, {response.msg}")
                return None
            
            fields = []
            for field in response.data.items:
                fields.append({
                    "field_id": field.field_id,
                    "field_name": field.field_name,
                    "type": field.type,
                    "property": field.property
                })
            
            logger.info(f"成功获取{len(fields)}个字段信息")
            return fields
            
        except Exception as e:
            logger.error(f"获取表格字段异常: {e}")
            return None
    
    async def list_records(self, app_token: str, table_id: str, page_size: int = 100) -> Optional[List[Dict]]:
        """获取数据表中的记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            page_size: 每页记录数，默认100
            
        Returns:
            记录列表，失败返回None
        """
        try:
            # 构建请求
            request = ListTableRecordRequest.builder() \
                .app_token(app_token) \
                .table_id(table_id) \
                .page_size(page_size) \
                .build()
            
            # 发起请求
            response = await self.client.bitable.v1.record.list(request)
            
            if not response.success():
                logger.error(f"获取记录列表失败: {response.code}, {response.msg}")
                return None
            
            records = []
            for record in response.data.items:
                records.append({
                    "record_id": record.record_id,
                    "fields": record.fields,
                    "created_time": record.created_time,
                    "last_modified_time": record.last_modified_time
                })
            
            logger.info(f"成功获取{len(records)}条记录")
            return records
            
        except Exception as e:
            logger.error(f"获取记录列表异常: {e}")
            return None
