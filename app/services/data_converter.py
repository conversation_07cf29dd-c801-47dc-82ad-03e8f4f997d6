import pandas as pd
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from io import BytesIO

logger = logging.getLogger(__name__)

class WeChatDataConverter:
    """微信公众号数据转换器，将Excel数据转换为飞书表格格式"""
    
    @staticmethod
    def parse_excel_data(excel_data: bytes) -> Optional[Dict[str, Any]]:
        """解析微信公众号Excel数据
        
        Args:
            excel_data: Excel文件的二进制数据
            
        Returns:
            解析后的数据字典，包含用户数据和文章数据
        """
        try:
            # 使用BytesIO读取Excel数据
            excel_file = BytesIO(excel_data)
            
            # 读取Excel文件，可能包含多个sheet
            excel_sheets = pd.read_excel(excel_file, sheet_name=None)
            
            result = {
                "user_summary": [],
                "article_summary": [],
                "raw_sheets": list(excel_sheets.keys())
            }
            
            # 遍历所有sheet
            for sheet_name, df in excel_sheets.items():
                logger.info(f"处理sheet: {sheet_name}, 行数: {len(df)}")
                
                # 根据sheet名称或列名判断数据类型
                if WeChatDataConverter._is_user_summary_sheet(sheet_name, df):
                    user_data = WeChatDataConverter._parse_user_summary(df)
                    result["user_summary"].extend(user_data)
                elif WeChatDataConverter._is_article_summary_sheet(sheet_name, df):
                    article_data = WeChatDataConverter._parse_article_summary(df)
                    result["article_summary"].extend(article_data)
                else:
                    logger.warning(f"未识别的sheet类型: {sheet_name}")
            
            logger.info(f"解析完成: 用户数据{len(result['user_summary'])}条, 文章数据{len(result['article_summary'])}条")
            return result
            
        except Exception as e:
            logger.error(f"解析Excel数据失败: {e}")
            return None
    
    @staticmethod
    def _is_user_summary_sheet(sheet_name: str, df: pd.DataFrame) -> bool:
        """判断是否为用户概况数据sheet"""
        # 检查sheet名称
        user_keywords = ["用户", "user", "summary", "概况", "粉丝"]
        if any(keyword in sheet_name.lower() for keyword in user_keywords):
            return True
        
        # 检查列名
        if not df.empty:
            columns = [str(col).lower() for col in df.columns]
            user_columns = ["新增用户", "取消关注", "净增长", "累计用户", "new_user", "cancel_user"]
            if any(keyword in " ".join(columns) for keyword in user_columns):
                return True
        
        return False
    
    @staticmethod
    def _is_article_summary_sheet(sheet_name: str, df: pd.DataFrame) -> bool:
        """判断是否为图文分析数据sheet"""
        # 检查sheet名称
        article_keywords = ["图文", "文章", "article", "content", "阅读"]
        if any(keyword in sheet_name.lower() for keyword in article_keywords):
            return True
        
        # 检查列名
        if not df.empty:
            columns = [str(col).lower() for col in df.columns]
            article_columns = ["标题", "阅读", "点赞", "分享", "title", "read", "like", "share"]
            if any(keyword in " ".join(columns) for keyword in article_columns):
                return True
        
        return False
    
    @staticmethod
    def _parse_user_summary(df: pd.DataFrame) -> List[Dict[str, Any]]:
        """解析用户概况数据"""
        try:
            records = []
            
            # 标准化列名映射
            column_mapping = {
                "日期": "date",
                "统计日期": "date", 
                "date": "date",
                "新增用户": "new_users",
                "新增关注人数": "new_users",
                "new_user": "new_users",
                "取消关注用户": "cancel_users", 
                "取消关注人数": "cancel_users",
                "cancel_user": "cancel_users",
                "净增长": "net_growth",
                "净增关注人数": "net_growth",
                "net_growth": "net_growth",
                "累计用户": "cumulative_users",
                "累计关注人数": "cumulative_users", 
                "total_user": "cumulative_users"
            }
            
            # 重命名列
            df_renamed = df.copy()
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df_renamed = df_renamed.rename(columns={old_name: new_name})
            
            # 处理每一行数据
            for _, row in df_renamed.iterrows():
                record = {}
                
                # 处理日期
                if "date" in row:
                    date_value = row["date"]
                    if pd.notna(date_value):
                        if isinstance(date_value, str):
                            # 尝试解析字符串日期
                            try:
                                record["日期"] = datetime.strptime(date_value, "%Y-%m-%d").strftime("%Y-%m-%d")
                            except:
                                record["日期"] = str(date_value)
                        else:
                            record["日期"] = date_value.strftime("%Y-%m-%d") if hasattr(date_value, 'strftime') else str(date_value)
                
                # 处理数值字段
                numeric_fields = {
                    "new_users": "新增用户数",
                    "cancel_users": "取消关注用户数", 
                    "net_growth": "净增长",
                    "cumulative_users": "累计用户数"
                }
                
                for field_key, field_name in numeric_fields.items():
                    if field_key in row:
                        value = row[field_key]
                        if pd.notna(value):
                            try:
                                record[field_name] = int(float(value))
                            except:
                                record[field_name] = 0
                        else:
                            record[field_name] = 0
                
                if record:  # 只添加非空记录
                    records.append(record)
            
            return records
            
        except Exception as e:
            logger.error(f"解析用户概况数据失败: {e}")
            return []
    
    @staticmethod
    def _parse_article_summary(df: pd.DataFrame) -> List[Dict[str, Any]]:
        """解析图文分析数据"""
        try:
            records = []
            
            # 标准化列名映射
            column_mapping = {
                "标题": "title",
                "文章标题": "title",
                "title": "title",
                "阅读数": "read_count",
                "阅读人数": "read_count", 
                "read_count": "read_count",
                "点赞数": "like_count",
                "like_count": "like_count",
                "分享数": "share_count", 
                "转发数": "share_count",
                "share_count": "share_count",
                "发布时间": "publish_time",
                "推送时间": "publish_time",
                "publish_time": "publish_time",
                "送达人数": "reach_count",
                "reach_count": "reach_count"
            }
            
            # 重命名列
            df_renamed = df.copy()
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df_renamed = df_renamed.rename(columns={old_name: new_name})
            
            # 处理每一行数据
            for _, row in df_renamed.iterrows():
                record = {}
                
                # 处理标题
                if "title" in row and pd.notna(row["title"]):
                    record["文章标题"] = str(row["title"])
                
                # 处理发布时间
                if "publish_time" in row:
                    time_value = row["publish_time"]
                    if pd.notna(time_value):
                        if isinstance(time_value, str):
                            record["发布时间"] = time_value
                        else:
                            record["发布时间"] = time_value.strftime("%Y-%m-%d %H:%M:%S") if hasattr(time_value, 'strftime') else str(time_value)
                
                # 处理数值字段
                numeric_fields = {
                    "read_count": "阅读数",
                    "like_count": "点赞数",
                    "share_count": "分享数",
                    "reach_count": "送达人数"
                }
                
                for field_key, field_name in numeric_fields.items():
                    if field_key in row:
                        value = row[field_key]
                        if pd.notna(value):
                            try:
                                record[field_name] = int(float(value))
                            except:
                                record[field_name] = 0
                        else:
                            record[field_name] = 0
                
                if record and "文章标题" in record:  # 只添加有标题的记录
                    records.append(record)
            
            return records
            
        except Exception as e:
            logger.error(f"解析图文分析数据失败: {e}")
            return []
    
    @staticmethod
    def prepare_feishu_records(data_type: str, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备飞书表格记录格式
        
        Args:
            data_type: 数据类型，'user_summary' 或 'article_summary'
            records: 原始记录列表
            
        Returns:
            飞书表格格式的记录列表
        """
        try:
            feishu_records = []
            
            for record in records:
                feishu_record = {}
                
                # 转换为飞书字段格式
                for field_name, field_value in record.items():
                    # 飞书字段值需要特定格式
                    if isinstance(field_value, (int, float)):
                        feishu_record[field_name] = field_value
                    elif isinstance(field_value, str):
                        feishu_record[field_name] = field_value
                    elif isinstance(field_value, datetime):
                        feishu_record[field_name] = int(field_value.timestamp() * 1000)  # 飞书时间戳格式
                    else:
                        feishu_record[field_name] = str(field_value)
                
                feishu_records.append(feishu_record)
            
            logger.info(f"准备了{len(feishu_records)}条飞书记录")
            return feishu_records
            
        except Exception as e:
            logger.error(f"准备飞书记录失败: {e}")
            return []
