# 微信公众号登录状态持久化

## 概述

本系统实现了微信公众号登录状态的持久化功能，确保服务重启后登录状态不会丢失。

## 功能特性

### 1. 自动状态保存
- ✅ 扫码登录成功后自动保存登录状态
- ✅ 包含cookies、localStorage和session信息
- ✅ 支持多账号独立存储

### 2. 智能状态恢复
- ✅ 服务启动时自动尝试恢复登录状态
- ✅ 检查状态有效性（7天过期机制）
- ✅ 自动清理过期状态

### 3. 持久化存储
- 📁 存储位置: `user_data/wechat_account_{account_id}/`
- 📄 状态文件: `login_state.json`
- 🔐 包含完整的浏览器会话信息

## 技术实现

### 核心原理

使用 Playwright 的浏览器上下文 (BrowserContext) 持久化功能：

```python
# 保存登录状态
storage_state = await self.context.storage_state()
login_state = {
    "cookies": cookies,
    "storage_state": storage_state,
    "saved_at": datetime.now().isoformat(),
    "account_id": self.account_id
}

# 恢复登录状态
self.context = await self.browser.new_context(
    storage_state=storage_state
)
```

### 目录结构

```
user_data/
├── wechat_account_1/
│   └── login_state.json
├── wechat_account_2/
│   └── login_state.json
└── default/
    └── login_state.json
```

### 状态文件格式

```json
{
  "cookies": [...],
  "storage_state": {
    "cookies": [...],
    "origins": [...]
  },
  "saved_at": "2025-07-18T10:30:00",
  "account_id": 1
}
```

## 使用方法

### 1. 基本使用

```python
# 创建服务实例（传入账号ID）
wechat_service = WeChatMPService(account_id=1)

# 获取二维码（会自动尝试恢复登录状态）
qr_code = await wechat_service.get_login_qrcode()

if qr_code == "already_logged_in":
    print("已经登录，无需扫码")
else:
    # 等待用户扫码
    await wechat_service.wait_for_login_complete()
```

### 2. 手动状态管理

```python
# 手动保存状态
await wechat_service.save_login_state()

# 手动加载状态
success = await wechat_service.load_login_state()

# 检查现有登录是否有效
is_valid = await wechat_service.check_existing_login()
```

### 3. 管理工具

使用提供的管理脚本：

```bash
# 运行管理工具
python manage_login_states.py

# 功能菜单：
# 1. 列出所有保存的登录状态
# 2. 清除指定账号的登录状态  
# 3. 清除所有登录状态
# 4. 备份登录状态
# 5. 从备份恢复登录状态
# 6. 检查登录状态有效性
```

## API 变更

### WeChatMPService 构造函数

**之前:**
```python
wechat_service = WeChatMPService()
```

**现在:**
```python
wechat_service = WeChatMPService(account_id=1)  # 支持持久化
```

### 新增方法

- `save_login_state()` - 保存登录状态
- `load_login_state()` - 加载登录状态  
- `check_existing_login()` - 检查登录有效性
- `_get_user_data_dir()` - 获取用户数据目录
- `_create_persistent_context()` - 创建持久化上下文
- `_init_browser()` - 初始化浏览器

## 后端集成

### 路由更新

所有 WeChatMPService 的创建都已更新为支持账号ID：

```python
# app/routers/wechat.py
wechat_service = WeChatMPService(account_id=account_id)

# 自动尝试加载已保存的登录状态
if not await wechat_service.load_login_state():
    # 如果没有有效状态，提示重新登录
    raise HTTPException(status_code=400, detail="登录会话已过期，请重新获取二维码登录")
```

## 安全考虑

### 1. 数据保护
- 🔐 登录状态文件存储在本地，不上传到版本控制
- 🚫 `.gitignore` 已配置忽略 `user_data/` 目录
- ⏰ 自动过期机制（7天后失效）

### 2. 隔离性
- 👤 每个账号使用独立的存储目录
- 🔒 不同账号的登录状态完全隔离
- 🧹 自动清理过期状态

### 3. 错误处理
- ❌ 状态文件损坏时自动回退到重新登录
- 🔄 网络异常时优雅降级
- 📝 详细的错误日志记录

## 测试脚本

### 1. 完整功能测试

```bash
python test_persistent_login.py
```

提供三种测试模式：
1. 测试登录和状态保存
2. 测试使用已保存状态下载数据  
3. 运行完整测试

### 2. 状态管理测试

```bash
python manage_login_states.py
```

## 故障排除

### 常见问题

1. **登录状态加载失败**
   - 检查 `user_data/` 目录权限
   - 确认状态文件未损坏
   - 验证账号ID是否正确

2. **状态过期**
   - 登录状态7天后自动过期
   - 可通过管理工具检查状态年龄
   - 重新扫码登录即可

3. **下载功能失败**
   - 确认登录状态有效
   - 检查网络连接
   - 验证日期范围合法性

### 调试方法

1. **启用详细日志**
   ```python
   # 服务会自动打印详细的状态信息
   print("登录状态已保存到: /path/to/state/file")
   print("检测到有效的登录状态")
   ```

2. **手动检查状态**
   ```bash
   # 使用管理工具检查
   python manage_login_states.py
   # 选择选项 6: 检查登录状态有效性
   ```

3. **清理重试**
   ```bash
   # 清除特定账号状态
   python manage_login_states.py
   # 选择选项 2，输入账号ID
   ```

## 性能优化

### 1. 启动速度
- ⚡ 有有效登录状态时，跳过二维码获取
- 🚀 快速状态验证（< 3秒）
- 💾 本地缓存减少网络请求

### 2. 资源管理
- 🧹 自动清理过期状态文件
- 📦 压缩存储格式
- 🔄 惰性加载机制

### 3. 并发支持
- 👥 支持多账号并发操作
- 🔒 状态文件独立存储
- ⚡ 异步操作不阻塞

## 未来扩展

### 计划功能

1. **加密存储** - 对敏感信息进行加密
2. **云端同步** - 支持状态文件云端备份
3. **自动刷新** - 接近过期时自动刷新状态
4. **监控告警** - 状态异常时发送通知

### 配置选项

未来可能添加的配置：

```python
class WeChatMPService:
    def __init__(
        self, 
        account_id: int,
        state_expiry_days: int = 7,  # 状态过期天数
        auto_backup: bool = True,    # 自动备份
        encrypt_state: bool = False  # 加密存储
    ):
        pass
```

## 总结

这个持久化实现显著改善了用户体验：

- ✅ **无需重复登录** - 服务重启后自动恢复
- ✅ **自动状态管理** - 智能过期和清理机制  
- ✅ **多账号支持** - 完全隔离的账号管理
- ✅ **安全可靠** - 本地存储，自动过期保护
- ✅ **易于管理** - 提供完整的管理工具

通过这些改进，用户只需要在首次使用或状态过期时扫码登录，大大提升了系统的可用性和用户体验。
