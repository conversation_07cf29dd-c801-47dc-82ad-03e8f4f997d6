# Code generated by Lark OpenAPI.

import hashlib
import json
from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.api.acs.v1.processor import *
from lark_oapi.api.application.v6.processor import *
from lark_oapi.api.approval.v4.processor import *
from lark_oapi.api.calendar.v4.processor import *
from lark_oapi.api.contact.v3.processor import *
from lark_oapi.api.corehr.v1.processor import *
from lark_oapi.api.corehr.v2.processor import *
from lark_oapi.api.drive.v1.processor import *
from lark_oapi.api.helpdesk.v1.processor import *
from lark_oapi.api.hire.v1.processor import *
from lark_oapi.api.im.v1.processor import *
from lark_oapi.api.mail.v1.processor import *
from lark_oapi.api.meeting_room.v1.processor import *
from lark_oapi.api.moments.v1.processor import *
from lark_oapi.api.payroll.v1.processor import *
from lark_oapi.api.performance.v2.processor import *
from lark_oapi.api.task.v1.processor import *
from lark_oapi.api.vc.v1.processor import *
from lark_oapi.core.const import *
from lark_oapi.core.enum import LogLevel
from lark_oapi.core.exception import *
from lark_oapi.core.http import HttpHandler
from lark_oapi.core.json import JSON
from lark_oapi.core.log import logger
from lark_oapi.core.model import RawRequest, RawResponse
from lark_oapi.core.utils import Strings, AESCipher
from .callback.model.p2_card_action_trigger import P2CardActionTriggerResponse
from .callback.model.p2_card_action_trigger import P2CardActionTrigger
from .callback.model.p2_url_preview_get import P2URLPreviewGet, P2URLPreviewGetResponse
from .callback.processor import P2CardActionTriggerProcessor, P2URLPreviewGetProcessor
from .context import EventContext
from .custom import CustomizedEventProcessor, CustomizedEvent
from .processor import ICallBackProcessor


class EventDispatcherHandler(HttpHandler):

	def __init__(self) -> None:
		self._processorMap: Dict[str, IEventProcessor] = {}
		self._callback_processor_map: Dict[str, ICallBackProcessor] = {}
		self._encrypt_key: Optional[str] = None
		self._verification_token: Optional[str] = None

	def do(self, req: RawRequest) -> RawResponse:
		logger.debug(f"event access, uri: {req.uri}, "
					 f"headers: {JSON.marshal(req.headers)}, "
					 f"body: {str(req.body, UTF_8) if req.body is not None else None}")

		resp = RawResponse()
		resp.status_code = 200
		resp.set_content_type(f"{APPLICATION_JSON}; charset=utf-8")

		try:
			if req.body is None:
				raise InvalidArgsException("request body is null")

			# 消息解密
			plaintext = self._decrypt(req.body)

			# 上下文结构化
			context = JSON.unmarshal(plaintext, EventContext)
			if Strings.is_not_empty(context.schema):
				# 解析 v2 事件
				context.schema = "p2"
				context.type = context.header.event_type
				context.token = context.header.token
			elif Strings.is_not_empty(context.uuid):
				# 解析 v1 事件
				context.schema = "p1"
				context.type = context.event.get("type")

			# 校验 token
			if context.token is not None and self._verification_token != context.token:
				raise AccessDeniedException("invalid verification_token")

			if URL_VERIFICATION == context.type:
				# 验证回调地址事件, 直接返回 Challenge Code
				resp_body = "{\"challenge\":\"%s\"}" % context.challenge
				resp.content = resp_body.encode(UTF_8)
				return resp
			else:
				# 否则验签
				self._verify_sign(req)

			event_key = f"{context.schema}.{context.type}"
			if event_key in self._callback_processor_map:
				processor: ICallBackProcessor = self._callback_processor_map.get(event_key)
				if processor is None:
					raise EventException(f"callback processor not found, type: {context.type}")

				# 消息反序列化
				data = JSON.unmarshal(plaintext, processor.type())
				result = processor.do(data)

				# 返回成功
				resp.content = JSON.marshal(result).encode(UTF_8)
			else:
				processor: IEventProcessor = self._processorMap.get(event_key)
				if processor is None:
					raise EventException(f"processor not found, type: {context.type}")

				# 消息反序列化
				data = JSON.unmarshal(plaintext, processor.type())
				processor.do(data)

				# 返回成功
				resp.content = "{\"msg\":\"success\"}".encode(UTF_8)
			return resp

		except Exception as e:
			logger.exception(
				f"handle event failed, uri: {req.uri}, request_id: {req.headers.get(X_REQUEST_ID)}, err: {e}")
			resp.status_code = 500
			resp_body = "{\"msg\":\"%s\"}" % str(e)
			resp.content = resp_body.encode(UTF_8)

			return resp

	def do_without_validation(self, payload: bytes) -> Any:
		pl = payload.decode(UTF_8)
		context = JSON.unmarshal(pl, EventContext)
		if Strings.is_not_empty(context.schema):
			# 解析 v2 事件
			context.schema = "p2"
			context.type = context.header.event_type
			context.token = context.header.token
		elif Strings.is_not_empty(context.uuid):
			# 解析 v1 事件
			context.schema = "p1"
			context.type = context.event.get("type")

		event_key = f"{context.schema}.{context.type}"
		if event_key in self._callback_processor_map:
			processor: ICallBackProcessor = self._callback_processor_map.get(event_key)
			if processor is None:
				raise EventException(f"callback processor not found, type: {context.type}")

			# 消息反序列化
			data = JSON.unmarshal(pl, processor.type())
			result = processor.do(data)

			# 返回成功
			return result
		else:
			processor: IEventProcessor = self._processorMap.get(f"{context.schema}.{context.type}")
			if processor is None:
				raise EventException(f"processor not found, type: {context.type}")

			# 消息反序列化
			data = JSON.unmarshal(pl, processor.type())
			processor.do(data)

	def _decrypt(self, content: bytes) -> str:
		plaintext: str
		encrypt = json.loads(content).get("encrypt")
		if Strings.is_not_empty(encrypt):
			if Strings.is_empty(self._encrypt_key):
				raise NoAuthorizationException("encrypt_key not found")
			plaintext = AESCipher(self._encrypt_key).decrypt_str(encrypt)
		else:
			plaintext = str(content, UTF_8)

		return plaintext

	def _verify_sign(self, request: RawRequest) -> None:
		if self._encrypt_key is None or self._encrypt_key == "":
			return
		timestamp = request.headers.get(LARK_REQUEST_TIMESTAMP)
		nonce = request.headers.get(LARK_REQUEST_NONCE)
		signature = request.headers.get(LARK_REQUEST_SIGNATURE)
		bs = (timestamp + nonce + self._encrypt_key).encode(UTF_8) + request.body
		h = hashlib.sha256(bs)
		if signature != h.hexdigest():
			raise AccessDeniedException("signature verification failed")

	@staticmethod
	def builder(encrypt_key: str, verification_token: str, level: LogLevel = None) -> "EventDispatcherHandlerBuilder":
		if level is not None:
			logger.setLevel(int(level.value))
		return EventDispatcherHandlerBuilder(encrypt_key, verification_token)


class EventDispatcherHandlerBuilder(object):
	def __init__(self, encrypt_key: str, verification_token: str) -> None:
		self._encrypt_key = encrypt_key
		self._verification_token = verification_token
		self._processorMap = {}
		self._callback_processor_map = {}

	def register_p1_customized_event(self, event_type: str,
									 f: Callable[[CustomizedEvent], None]) -> "EventDispatcherHandlerBuilder":
		t = f"p1.{event_type}"
		if t in self._processorMap:
			raise EventException(f"processor already registered, type: {t}")
		self._processorMap[t] = CustomizedEventProcessor(f)
		return self

	def register_p2_customized_event(self, event_type: str,
									 f: Callable[[CustomizedEvent], None]) -> "EventDispatcherHandlerBuilder":
		t = f"p2.{event_type}"
		if t in self._processorMap:
			raise EventException(f"processor already registered, type: {t}")
		self._processorMap[t] = CustomizedEventProcessor(f)
		return self

	def register_p2_card_action_trigger(self, f: Callable[
		[P2CardActionTrigger], P2CardActionTriggerResponse]) -> "EventDispatcherHandlerBuilder":
		if "p2.card.action.trigger" in self._callback_processor_map:
			raise EventException("processor already registered, type: p2.card.action.trigger")
		self._callback_processor_map["p2.card.action.trigger"] = P2CardActionTriggerProcessor(f)
		return self

	def register_p2_url_preview_get(self, f: Callable[
		[P2URLPreviewGet], P2URLPreviewGetResponse]) -> "EventDispatcherHandlerBuilder":
		if "p2.url.preview.get" in self._callback_processor_map:
			raise EventException("processor already registered, type: p2.url.preview.get")
		self._callback_processor_map["p2.url.preview.get"] = P2URLPreviewGetProcessor(f)
		return self

	
	def register_p2_acs_access_record_created_v1(self, f: Callable[[P2AcsAccessRecordCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.acs.access_record.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.acs.access_record.created_v1")
		self._processorMap["p2.acs.access_record.created_v1"] = P2AcsAccessRecordCreatedV1Processor(f)
		return self
	
	def register_p2_acs_user_updated_v1(self, f: Callable[[P2AcsUserUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.acs.user.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.acs.user.updated_v1")
		self._processorMap["p2.acs.user.updated_v1"] = P2AcsUserUpdatedV1Processor(f)
		return self
	
	def register_p2_application_application_created_v6(self, f: Callable[[P2ApplicationApplicationCreatedV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.created_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.created_v6")
		self._processorMap["p2.application.application.created_v6"] = P2ApplicationApplicationCreatedV6Processor(f)
		return self
	
	def register_p2_application_application_app_version_audit_v6(self, f: Callable[[P2ApplicationApplicationAppVersionAuditV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.app_version.audit_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.app_version.audit_v6")
		self._processorMap["p2.application.application.app_version.audit_v6"] = P2ApplicationApplicationAppVersionAuditV6Processor(f)
		return self
	
	def register_p2_application_application_app_version_publish_apply_v6(self, f: Callable[[P2ApplicationApplicationAppVersionPublishApplyV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.app_version.publish_apply_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.app_version.publish_apply_v6")
		self._processorMap["p2.application.application.app_version.publish_apply_v6"] = P2ApplicationApplicationAppVersionPublishApplyV6Processor(f)
		return self
	
	def register_p2_application_application_app_version_publish_revoke_v6(self, f: Callable[[P2ApplicationApplicationAppVersionPublishRevokeV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.app_version.publish_revoke_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.app_version.publish_revoke_v6")
		self._processorMap["p2.application.application.app_version.publish_revoke_v6"] = P2ApplicationApplicationAppVersionPublishRevokeV6Processor(f)
		return self
	
	def register_p2_application_application_feedback_created_v6(self, f: Callable[[P2ApplicationApplicationFeedbackCreatedV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.feedback.created_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.feedback.created_v6")
		self._processorMap["p2.application.application.feedback.created_v6"] = P2ApplicationApplicationFeedbackCreatedV6Processor(f)
		return self
	
	def register_p2_application_application_feedback_updated_v6(self, f: Callable[[P2ApplicationApplicationFeedbackUpdatedV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.feedback.updated_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.feedback.updated_v6")
		self._processorMap["p2.application.application.feedback.updated_v6"] = P2ApplicationApplicationFeedbackUpdatedV6Processor(f)
		return self
	
	def register_p2_application_application_visibility_added_v6(self, f: Callable[[P2ApplicationApplicationVisibilityAddedV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.application.visibility.added_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.application.visibility.added_v6")
		self._processorMap["p2.application.application.visibility.added_v6"] = P2ApplicationApplicationVisibilityAddedV6Processor(f)
		return self
	
	def register_p2_application_bot_menu_v6(self, f: Callable[[P2ApplicationBotMenuV6], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.application.bot.menu_v6" in self._processorMap:
			raise EventException("processor already registered, type: p2.application.bot.menu_v6")
		self._processorMap["p2.application.bot.menu_v6"] = P2ApplicationBotMenuV6Processor(f)
		return self
	
	def register_p2_approval_approval_updated_v4(self, f: Callable[[P2ApprovalApprovalUpdatedV4], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.approval.approval.updated_v4" in self._processorMap:
			raise EventException("processor already registered, type: p2.approval.approval.updated_v4")
		self._processorMap["p2.approval.approval.updated_v4"] = P2ApprovalApprovalUpdatedV4Processor(f)
		return self
	
	def register_p2_calendar_calendar_changed_v4(self, f: Callable[[P2CalendarCalendarChangedV4], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.calendar.calendar.changed_v4" in self._processorMap:
			raise EventException("processor already registered, type: p2.calendar.calendar.changed_v4")
		self._processorMap["p2.calendar.calendar.changed_v4"] = P2CalendarCalendarChangedV4Processor(f)
		return self
	
	def register_p2_calendar_calendar_acl_created_v4(self, f: Callable[[P2CalendarCalendarAclCreatedV4], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.calendar.calendar.acl.created_v4" in self._processorMap:
			raise EventException("processor already registered, type: p2.calendar.calendar.acl.created_v4")
		self._processorMap["p2.calendar.calendar.acl.created_v4"] = P2CalendarCalendarAclCreatedV4Processor(f)
		return self
	
	def register_p2_calendar_calendar_acl_deleted_v4(self, f: Callable[[P2CalendarCalendarAclDeletedV4], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.calendar.calendar.acl.deleted_v4" in self._processorMap:
			raise EventException("processor already registered, type: p2.calendar.calendar.acl.deleted_v4")
		self._processorMap["p2.calendar.calendar.acl.deleted_v4"] = P2CalendarCalendarAclDeletedV4Processor(f)
		return self
	
	def register_p2_calendar_calendar_event_changed_v4(self, f: Callable[[P2CalendarCalendarEventChangedV4], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.calendar.calendar.event.changed_v4" in self._processorMap:
			raise EventException("processor already registered, type: p2.calendar.calendar.event.changed_v4")
		self._processorMap["p2.calendar.calendar.event.changed_v4"] = P2CalendarCalendarEventChangedV4Processor(f)
		return self
	
	def register_p2_contact_custom_attr_event_updated_v3(self, f: Callable[[P2ContactCustomAttrEventUpdatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.custom_attr_event.updated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.custom_attr_event.updated_v3")
		self._processorMap["p2.contact.custom_attr_event.updated_v3"] = P2ContactCustomAttrEventUpdatedV3Processor(f)
		return self
	
	def register_p2_contact_department_created_v3(self, f: Callable[[P2ContactDepartmentCreatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.department.created_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.department.created_v3")
		self._processorMap["p2.contact.department.created_v3"] = P2ContactDepartmentCreatedV3Processor(f)
		return self
	
	def register_p2_contact_department_deleted_v3(self, f: Callable[[P2ContactDepartmentDeletedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.department.deleted_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.department.deleted_v3")
		self._processorMap["p2.contact.department.deleted_v3"] = P2ContactDepartmentDeletedV3Processor(f)
		return self
	
	def register_p2_contact_department_updated_v3(self, f: Callable[[P2ContactDepartmentUpdatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.department.updated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.department.updated_v3")
		self._processorMap["p2.contact.department.updated_v3"] = P2ContactDepartmentUpdatedV3Processor(f)
		return self
	
	def register_p2_contact_employee_type_enum_actived_v3(self, f: Callable[[P2ContactEmployeeTypeEnumActivedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.employee_type_enum.actived_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.employee_type_enum.actived_v3")
		self._processorMap["p2.contact.employee_type_enum.actived_v3"] = P2ContactEmployeeTypeEnumActivedV3Processor(f)
		return self
	
	def register_p2_contact_employee_type_enum_created_v3(self, f: Callable[[P2ContactEmployeeTypeEnumCreatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.employee_type_enum.created_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.employee_type_enum.created_v3")
		self._processorMap["p2.contact.employee_type_enum.created_v3"] = P2ContactEmployeeTypeEnumCreatedV3Processor(f)
		return self
	
	def register_p2_contact_employee_type_enum_deactivated_v3(self, f: Callable[[P2ContactEmployeeTypeEnumDeactivatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.employee_type_enum.deactivated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.employee_type_enum.deactivated_v3")
		self._processorMap["p2.contact.employee_type_enum.deactivated_v3"] = P2ContactEmployeeTypeEnumDeactivatedV3Processor(f)
		return self
	
	def register_p2_contact_employee_type_enum_deleted_v3(self, f: Callable[[P2ContactEmployeeTypeEnumDeletedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.employee_type_enum.deleted_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.employee_type_enum.deleted_v3")
		self._processorMap["p2.contact.employee_type_enum.deleted_v3"] = P2ContactEmployeeTypeEnumDeletedV3Processor(f)
		return self
	
	def register_p2_contact_employee_type_enum_updated_v3(self, f: Callable[[P2ContactEmployeeTypeEnumUpdatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.employee_type_enum.updated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.employee_type_enum.updated_v3")
		self._processorMap["p2.contact.employee_type_enum.updated_v3"] = P2ContactEmployeeTypeEnumUpdatedV3Processor(f)
		return self
	
	def register_p2_contact_scope_updated_v3(self, f: Callable[[P2ContactScopeUpdatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.scope.updated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.scope.updated_v3")
		self._processorMap["p2.contact.scope.updated_v3"] = P2ContactScopeUpdatedV3Processor(f)
		return self
	
	def register_p2_contact_user_created_v3(self, f: Callable[[P2ContactUserCreatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.user.created_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.user.created_v3")
		self._processorMap["p2.contact.user.created_v3"] = P2ContactUserCreatedV3Processor(f)
		return self
	
	def register_p2_contact_user_deleted_v3(self, f: Callable[[P2ContactUserDeletedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.user.deleted_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.user.deleted_v3")
		self._processorMap["p2.contact.user.deleted_v3"] = P2ContactUserDeletedV3Processor(f)
		return self
	
	def register_p2_contact_user_updated_v3(self, f: Callable[[P2ContactUserUpdatedV3], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.contact.user.updated_v3" in self._processorMap:
			raise EventException("processor already registered, type: p2.contact.user.updated_v3")
		self._processorMap["p2.contact.user.updated_v3"] = P2ContactUserUpdatedV3Processor(f)
		return self
	
	def register_p2_corehr_common_data_meta_data_updated_v1(self, f: Callable[[P2CorehrCommonDataMetaDataUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.common_data.meta_data.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.common_data.meta_data.updated_v1")
		self._processorMap["p2.corehr.common_data.meta_data.updated_v1"] = P2CorehrCommonDataMetaDataUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_contract_created_v1(self, f: Callable[[P2CorehrContractCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.contract.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.contract.created_v1")
		self._processorMap["p2.corehr.contract.created_v1"] = P2CorehrContractCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_contract_deleted_v1(self, f: Callable[[P2CorehrContractDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.contract.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.contract.deleted_v1")
		self._processorMap["p2.corehr.contract.deleted_v1"] = P2CorehrContractDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_contract_updated_v1(self, f: Callable[[P2CorehrContractUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.contract.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.contract.updated_v1")
		self._processorMap["p2.corehr.contract.updated_v1"] = P2CorehrContractUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_department_created_v1(self, f: Callable[[P2CorehrDepartmentCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.department.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.department.created_v1")
		self._processorMap["p2.corehr.department.created_v1"] = P2CorehrDepartmentCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_department_deleted_v1(self, f: Callable[[P2CorehrDepartmentDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.department.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.department.deleted_v1")
		self._processorMap["p2.corehr.department.deleted_v1"] = P2CorehrDepartmentDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_department_updated_v1(self, f: Callable[[P2CorehrDepartmentUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.department.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.department.updated_v1")
		self._processorMap["p2.corehr.department.updated_v1"] = P2CorehrDepartmentUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_employment_converted_v1(self, f: Callable[[P2CorehrEmploymentConvertedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employment.converted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employment.converted_v1")
		self._processorMap["p2.corehr.employment.converted_v1"] = P2CorehrEmploymentConvertedV1Processor(f)
		return self
	
	def register_p2_corehr_employment_created_v1(self, f: Callable[[P2CorehrEmploymentCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employment.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employment.created_v1")
		self._processorMap["p2.corehr.employment.created_v1"] = P2CorehrEmploymentCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_employment_deleted_v1(self, f: Callable[[P2CorehrEmploymentDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employment.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employment.deleted_v1")
		self._processorMap["p2.corehr.employment.deleted_v1"] = P2CorehrEmploymentDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_employment_resigned_v1(self, f: Callable[[P2CorehrEmploymentResignedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employment.resigned_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employment.resigned_v1")
		self._processorMap["p2.corehr.employment.resigned_v1"] = P2CorehrEmploymentResignedV1Processor(f)
		return self
	
	def register_p2_corehr_employment_updated_v1(self, f: Callable[[P2CorehrEmploymentUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employment.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employment.updated_v1")
		self._processorMap["p2.corehr.employment.updated_v1"] = P2CorehrEmploymentUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_job_created_v1(self, f: Callable[[P2CorehrJobCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job.created_v1")
		self._processorMap["p2.corehr.job.created_v1"] = P2CorehrJobCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_job_deleted_v1(self, f: Callable[[P2CorehrJobDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job.deleted_v1")
		self._processorMap["p2.corehr.job.deleted_v1"] = P2CorehrJobDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_job_updated_v1(self, f: Callable[[P2CorehrJobUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job.updated_v1")
		self._processorMap["p2.corehr.job.updated_v1"] = P2CorehrJobUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_job_change_updated_v1(self, f: Callable[[P2CorehrJobChangeUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_change.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_change.updated_v1")
		self._processorMap["p2.corehr.job_change.updated_v1"] = P2CorehrJobChangeUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_job_data_changed_v1(self, f: Callable[[P2CorehrJobDataChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_data.changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_data.changed_v1")
		self._processorMap["p2.corehr.job_data.changed_v1"] = P2CorehrJobDataChangedV1Processor(f)
		return self
	
	def register_p2_corehr_job_data_created_v1(self, f: Callable[[P2CorehrJobDataCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_data.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_data.created_v1")
		self._processorMap["p2.corehr.job_data.created_v1"] = P2CorehrJobDataCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_job_data_deleted_v1(self, f: Callable[[P2CorehrJobDataDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_data.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_data.deleted_v1")
		self._processorMap["p2.corehr.job_data.deleted_v1"] = P2CorehrJobDataDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_job_data_employed_v1(self, f: Callable[[P2CorehrJobDataEmployedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_data.employed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_data.employed_v1")
		self._processorMap["p2.corehr.job_data.employed_v1"] = P2CorehrJobDataEmployedV1Processor(f)
		return self
	
	def register_p2_corehr_job_data_updated_v1(self, f: Callable[[P2CorehrJobDataUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_data.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_data.updated_v1")
		self._processorMap["p2.corehr.job_data.updated_v1"] = P2CorehrJobDataUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_offboarding_updated_v1(self, f: Callable[[P2CorehrOffboardingUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.offboarding.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.offboarding.updated_v1")
		self._processorMap["p2.corehr.offboarding.updated_v1"] = P2CorehrOffboardingUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_org_role_authorization_updated_v1(self, f: Callable[[P2CorehrOrgRoleAuthorizationUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.org_role_authorization.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.org_role_authorization.updated_v1")
		self._processorMap["p2.corehr.org_role_authorization.updated_v1"] = P2CorehrOrgRoleAuthorizationUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_person_created_v1(self, f: Callable[[P2CorehrPersonCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.person.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.person.created_v1")
		self._processorMap["p2.corehr.person.created_v1"] = P2CorehrPersonCreatedV1Processor(f)
		return self
	
	def register_p2_corehr_person_deleted_v1(self, f: Callable[[P2CorehrPersonDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.person.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.person.deleted_v1")
		self._processorMap["p2.corehr.person.deleted_v1"] = P2CorehrPersonDeletedV1Processor(f)
		return self
	
	def register_p2_corehr_person_updated_v1(self, f: Callable[[P2CorehrPersonUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.person.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.person.updated_v1")
		self._processorMap["p2.corehr.person.updated_v1"] = P2CorehrPersonUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_pre_hire_updated_v1(self, f: Callable[[P2CorehrPreHireUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.pre_hire.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.pre_hire.updated_v1")
		self._processorMap["p2.corehr.pre_hire.updated_v1"] = P2CorehrPreHireUpdatedV1Processor(f)
		return self
	
	def register_p2_corehr_approval_groups_updated_v2(self, f: Callable[[P2CorehrApprovalGroupsUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.approval_groups.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.approval_groups.updated_v2")
		self._processorMap["p2.corehr.approval_groups.updated_v2"] = P2CorehrApprovalGroupsUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_company_created_v2(self, f: Callable[[P2CorehrCompanyCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.company.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.company.created_v2")
		self._processorMap["p2.corehr.company.created_v2"] = P2CorehrCompanyCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_company_deleted_v2(self, f: Callable[[P2CorehrCompanyDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.company.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.company.deleted_v2")
		self._processorMap["p2.corehr.company.deleted_v2"] = P2CorehrCompanyDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_company_updated_v2(self, f: Callable[[P2CorehrCompanyUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.company.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.company.updated_v2")
		self._processorMap["p2.corehr.company.updated_v2"] = P2CorehrCompanyUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_cost_center_created_v2(self, f: Callable[[P2CorehrCostCenterCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.cost_center.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.cost_center.created_v2")
		self._processorMap["p2.corehr.cost_center.created_v2"] = P2CorehrCostCenterCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_cost_center_deleted_v2(self, f: Callable[[P2CorehrCostCenterDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.cost_center.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.cost_center.deleted_v2")
		self._processorMap["p2.corehr.cost_center.deleted_v2"] = P2CorehrCostCenterDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_cost_center_updated_v2(self, f: Callable[[P2CorehrCostCenterUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.cost_center.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.cost_center.updated_v2")
		self._processorMap["p2.corehr.cost_center.updated_v2"] = P2CorehrCostCenterUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_custom_org_created_v2(self, f: Callable[[P2CorehrCustomOrgCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.custom_org.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.custom_org.created_v2")
		self._processorMap["p2.corehr.custom_org.created_v2"] = P2CorehrCustomOrgCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_custom_org_deleted_v2(self, f: Callable[[P2CorehrCustomOrgDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.custom_org.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.custom_org.deleted_v2")
		self._processorMap["p2.corehr.custom_org.deleted_v2"] = P2CorehrCustomOrgDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_custom_org_updated_v2(self, f: Callable[[P2CorehrCustomOrgUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.custom_org.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.custom_org.updated_v2")
		self._processorMap["p2.corehr.custom_org.updated_v2"] = P2CorehrCustomOrgUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_department_created_v2(self, f: Callable[[P2CorehrDepartmentCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.department.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.department.created_v2")
		self._processorMap["p2.corehr.department.created_v2"] = P2CorehrDepartmentCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_department_updated_v2(self, f: Callable[[P2CorehrDepartmentUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.department.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.department.updated_v2")
		self._processorMap["p2.corehr.department.updated_v2"] = P2CorehrDepartmentUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_employee_domain_event_v2(self, f: Callable[[P2CorehrEmployeeDomainEventV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.employee.domain_event_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.employee.domain_event_v2")
		self._processorMap["p2.corehr.employee.domain_event_v2"] = P2CorehrEmployeeDomainEventV2Processor(f)
		return self
	
	def register_p2_corehr_job_change_status_updated_v2(self, f: Callable[[P2CorehrJobChangeStatusUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_change.status_updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_change.status_updated_v2")
		self._processorMap["p2.corehr.job_change.status_updated_v2"] = P2CorehrJobChangeStatusUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_change_updated_v2(self, f: Callable[[P2CorehrJobChangeUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_change.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_change.updated_v2")
		self._processorMap["p2.corehr.job_change.updated_v2"] = P2CorehrJobChangeUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_family_created_v2(self, f: Callable[[P2CorehrJobFamilyCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_family.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_family.created_v2")
		self._processorMap["p2.corehr.job_family.created_v2"] = P2CorehrJobFamilyCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_family_deleted_v2(self, f: Callable[[P2CorehrJobFamilyDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_family.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_family.deleted_v2")
		self._processorMap["p2.corehr.job_family.deleted_v2"] = P2CorehrJobFamilyDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_job_family_updated_v2(self, f: Callable[[P2CorehrJobFamilyUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_family.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_family.updated_v2")
		self._processorMap["p2.corehr.job_family.updated_v2"] = P2CorehrJobFamilyUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_grade_created_v2(self, f: Callable[[P2CorehrJobGradeCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_grade.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_grade.created_v2")
		self._processorMap["p2.corehr.job_grade.created_v2"] = P2CorehrJobGradeCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_grade_deleted_v2(self, f: Callable[[P2CorehrJobGradeDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_grade.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_grade.deleted_v2")
		self._processorMap["p2.corehr.job_grade.deleted_v2"] = P2CorehrJobGradeDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_job_grade_updated_v2(self, f: Callable[[P2CorehrJobGradeUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_grade.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_grade.updated_v2")
		self._processorMap["p2.corehr.job_grade.updated_v2"] = P2CorehrJobGradeUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_level_created_v2(self, f: Callable[[P2CorehrJobLevelCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_level.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_level.created_v2")
		self._processorMap["p2.corehr.job_level.created_v2"] = P2CorehrJobLevelCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_job_level_deleted_v2(self, f: Callable[[P2CorehrJobLevelDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_level.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_level.deleted_v2")
		self._processorMap["p2.corehr.job_level.deleted_v2"] = P2CorehrJobLevelDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_job_level_updated_v2(self, f: Callable[[P2CorehrJobLevelUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.job_level.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.job_level.updated_v2")
		self._processorMap["p2.corehr.job_level.updated_v2"] = P2CorehrJobLevelUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_location_created_v2(self, f: Callable[[P2CorehrLocationCreatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.location.created_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.location.created_v2")
		self._processorMap["p2.corehr.location.created_v2"] = P2CorehrLocationCreatedV2Processor(f)
		return self
	
	def register_p2_corehr_location_deleted_v2(self, f: Callable[[P2CorehrLocationDeletedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.location.deleted_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.location.deleted_v2")
		self._processorMap["p2.corehr.location.deleted_v2"] = P2CorehrLocationDeletedV2Processor(f)
		return self
	
	def register_p2_corehr_location_updated_v2(self, f: Callable[[P2CorehrLocationUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.location.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.location.updated_v2")
		self._processorMap["p2.corehr.location.updated_v2"] = P2CorehrLocationUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_offboarding_checklist_updated_v2(self, f: Callable[[P2CorehrOffboardingChecklistUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.offboarding.checklist_updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.offboarding.checklist_updated_v2")
		self._processorMap["p2.corehr.offboarding.checklist_updated_v2"] = P2CorehrOffboardingChecklistUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_offboarding_status_updated_v2(self, f: Callable[[P2CorehrOffboardingStatusUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.offboarding.status_updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.offboarding.status_updated_v2")
		self._processorMap["p2.corehr.offboarding.status_updated_v2"] = P2CorehrOffboardingStatusUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_offboarding_updated_v2(self, f: Callable[[P2CorehrOffboardingUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.offboarding.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.offboarding.updated_v2")
		self._processorMap["p2.corehr.offboarding.updated_v2"] = P2CorehrOffboardingUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_pre_hire_onboarding_task_changed_v2(self, f: Callable[[P2CorehrPreHireOnboardingTaskChangedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.pre_hire.onboarding_task_changed_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.pre_hire.onboarding_task_changed_v2")
		self._processorMap["p2.corehr.pre_hire.onboarding_task_changed_v2"] = P2CorehrPreHireOnboardingTaskChangedV2Processor(f)
		return self
	
	def register_p2_corehr_probation_updated_v2(self, f: Callable[[P2CorehrProbationUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.probation.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.probation.updated_v2")
		self._processorMap["p2.corehr.probation.updated_v2"] = P2CorehrProbationUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_process_updated_v2(self, f: Callable[[P2CorehrProcessUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.process.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.process.updated_v2")
		self._processorMap["p2.corehr.process.updated_v2"] = P2CorehrProcessUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_process_approver_updated_v2(self, f: Callable[[P2CorehrProcessApproverUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.process.approver.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.process.approver.updated_v2")
		self._processorMap["p2.corehr.process.approver.updated_v2"] = P2CorehrProcessApproverUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_process_cc_updated_v2(self, f: Callable[[P2CorehrProcessCcUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.process.cc.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.process.cc.updated_v2")
		self._processorMap["p2.corehr.process.cc.updated_v2"] = P2CorehrProcessCcUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_process_node_updated_v2(self, f: Callable[[P2CorehrProcessNodeUpdatedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.process.node.updated_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.process.node.updated_v2")
		self._processorMap["p2.corehr.process.node.updated_v2"] = P2CorehrProcessNodeUpdatedV2Processor(f)
		return self
	
	def register_p2_corehr_process_status_update_v2(self, f: Callable[[P2CorehrProcessStatusUpdateV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.corehr.process.status.update_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.corehr.process.status.update_v2")
		self._processorMap["p2.corehr.process.status.update_v2"] = P2CorehrProcessStatusUpdateV2Processor(f)
		return self
	
	def register_p2_drive_file_bitable_field_changed_v1(self, f: Callable[[P2DriveFileBitableFieldChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.bitable_field_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.bitable_field_changed_v1")
		self._processorMap["p2.drive.file.bitable_field_changed_v1"] = P2DriveFileBitableFieldChangedV1Processor(f)
		return self
	
	def register_p2_drive_file_bitable_record_changed_v1(self, f: Callable[[P2DriveFileBitableRecordChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.bitable_record_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.bitable_record_changed_v1")
		self._processorMap["p2.drive.file.bitable_record_changed_v1"] = P2DriveFileBitableRecordChangedV1Processor(f)
		return self
	
	def register_p2_drive_file_created_in_folder_v1(self, f: Callable[[P2DriveFileCreatedInFolderV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.created_in_folder_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.created_in_folder_v1")
		self._processorMap["p2.drive.file.created_in_folder_v1"] = P2DriveFileCreatedInFolderV1Processor(f)
		return self
	
	def register_p2_drive_file_deleted_v1(self, f: Callable[[P2DriveFileDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.deleted_v1")
		self._processorMap["p2.drive.file.deleted_v1"] = P2DriveFileDeletedV1Processor(f)
		return self
	
	def register_p2_drive_file_edit_v1(self, f: Callable[[P2DriveFileEditV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.edit_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.edit_v1")
		self._processorMap["p2.drive.file.edit_v1"] = P2DriveFileEditV1Processor(f)
		return self
	
	def register_p2_drive_file_permission_member_added_v1(self, f: Callable[[P2DriveFilePermissionMemberAddedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.permission_member_added_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.permission_member_added_v1")
		self._processorMap["p2.drive.file.permission_member_added_v1"] = P2DriveFilePermissionMemberAddedV1Processor(f)
		return self
	
	def register_p2_drive_file_permission_member_applied_v1(self, f: Callable[[P2DriveFilePermissionMemberAppliedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.permission_member_applied_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.permission_member_applied_v1")
		self._processorMap["p2.drive.file.permission_member_applied_v1"] = P2DriveFilePermissionMemberAppliedV1Processor(f)
		return self
	
	def register_p2_drive_file_permission_member_removed_v1(self, f: Callable[[P2DriveFilePermissionMemberRemovedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.permission_member_removed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.permission_member_removed_v1")
		self._processorMap["p2.drive.file.permission_member_removed_v1"] = P2DriveFilePermissionMemberRemovedV1Processor(f)
		return self
	
	def register_p2_drive_file_read_v1(self, f: Callable[[P2DriveFileReadV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.read_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.read_v1")
		self._processorMap["p2.drive.file.read_v1"] = P2DriveFileReadV1Processor(f)
		return self
	
	def register_p2_drive_file_title_updated_v1(self, f: Callable[[P2DriveFileTitleUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.title_updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.title_updated_v1")
		self._processorMap["p2.drive.file.title_updated_v1"] = P2DriveFileTitleUpdatedV1Processor(f)
		return self
	
	def register_p2_drive_file_trashed_v1(self, f: Callable[[P2DriveFileTrashedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.drive.file.trashed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.drive.file.trashed_v1")
		self._processorMap["p2.drive.file.trashed_v1"] = P2DriveFileTrashedV1Processor(f)
		return self
	
	def register_p2_helpdesk_notification_approve_v1(self, f: Callable[[P2HelpdeskNotificationApproveV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.helpdesk.notification.approve_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.helpdesk.notification.approve_v1")
		self._processorMap["p2.helpdesk.notification.approve_v1"] = P2HelpdeskNotificationApproveV1Processor(f)
		return self
	
	def register_p2_helpdesk_ticket_created_v1(self, f: Callable[[P2HelpdeskTicketCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.helpdesk.ticket.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.helpdesk.ticket.created_v1")
		self._processorMap["p2.helpdesk.ticket.created_v1"] = P2HelpdeskTicketCreatedV1Processor(f)
		return self
	
	def register_p2_helpdesk_ticket_updated_v1(self, f: Callable[[P2HelpdeskTicketUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.helpdesk.ticket.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.helpdesk.ticket.updated_v1")
		self._processorMap["p2.helpdesk.ticket.updated_v1"] = P2HelpdeskTicketUpdatedV1Processor(f)
		return self
	
	def register_p2_helpdesk_ticket_message_created_v1(self, f: Callable[[P2HelpdeskTicketMessageCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.helpdesk.ticket_message.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.helpdesk.ticket_message.created_v1")
		self._processorMap["p2.helpdesk.ticket_message.created_v1"] = P2HelpdeskTicketMessageCreatedV1Processor(f)
		return self
	
	def register_p2_hire_application_deleted_v1(self, f: Callable[[P2HireApplicationDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.application.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.application.deleted_v1")
		self._processorMap["p2.hire.application.deleted_v1"] = P2HireApplicationDeletedV1Processor(f)
		return self
	
	def register_p2_hire_application_stage_changed_v1(self, f: Callable[[P2HireApplicationStageChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.application.stage_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.application.stage_changed_v1")
		self._processorMap["p2.hire.application.stage_changed_v1"] = P2HireApplicationStageChangedV1Processor(f)
		return self
	
	def register_p2_hire_eco_account_created_v1(self, f: Callable[[P2HireEcoAccountCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.eco_account.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.eco_account.created_v1")
		self._processorMap["p2.hire.eco_account.created_v1"] = P2HireEcoAccountCreatedV1Processor(f)
		return self
	
	def register_p2_hire_eco_background_check_canceled_v1(self, f: Callable[[P2HireEcoBackgroundCheckCanceledV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.eco_background_check.canceled_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.eco_background_check.canceled_v1")
		self._processorMap["p2.hire.eco_background_check.canceled_v1"] = P2HireEcoBackgroundCheckCanceledV1Processor(f)
		return self
	
	def register_p2_hire_eco_background_check_created_v1(self, f: Callable[[P2HireEcoBackgroundCheckCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.eco_background_check.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.eco_background_check.created_v1")
		self._processorMap["p2.hire.eco_background_check.created_v1"] = P2HireEcoBackgroundCheckCreatedV1Processor(f)
		return self
	
	def register_p2_hire_eco_exam_created_v1(self, f: Callable[[P2HireEcoExamCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.eco_exam.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.eco_exam.created_v1")
		self._processorMap["p2.hire.eco_exam.created_v1"] = P2HireEcoExamCreatedV1Processor(f)
		return self
	
	def register_p2_hire_ehr_import_task_imported_v1(self, f: Callable[[P2HireEhrImportTaskImportedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.ehr_import_task.imported_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.ehr_import_task.imported_v1")
		self._processorMap["p2.hire.ehr_import_task.imported_v1"] = P2HireEhrImportTaskImportedV1Processor(f)
		return self
	
	def register_p2_hire_ehr_import_task_for_internship_offer_imported_v1(self, f: Callable[[P2HireEhrImportTaskForInternshipOfferImportedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.ehr_import_task_for_internship_offer.imported_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.ehr_import_task_for_internship_offer.imported_v1")
		self._processorMap["p2.hire.ehr_import_task_for_internship_offer.imported_v1"] = P2HireEhrImportTaskForInternshipOfferImportedV1Processor(f)
		return self
	
	def register_p2_hire_offer_status_changed_v1(self, f: Callable[[P2HireOfferStatusChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.offer.status_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.offer.status_changed_v1")
		self._processorMap["p2.hire.offer.status_changed_v1"] = P2HireOfferStatusChangedV1Processor(f)
		return self
	
	def register_p2_hire_referral_account_assets_update_v1(self, f: Callable[[P2HireReferralAccountAssetsUpdateV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.referral_account.assets_update_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.referral_account.assets_update_v1")
		self._processorMap["p2.hire.referral_account.assets_update_v1"] = P2HireReferralAccountAssetsUpdateV1Processor(f)
		return self
	
	def register_p2_hire_talent_deleted_v1(self, f: Callable[[P2HireTalentDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.talent.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.talent.deleted_v1")
		self._processorMap["p2.hire.talent.deleted_v1"] = P2HireTalentDeletedV1Processor(f)
		return self
	
	def register_p2_hire_talent_tag_subscription_v1(self, f: Callable[[P2HireTalentTagSubscriptionV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.hire.talent.tag_subscription_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.hire.talent.tag_subscription_v1")
		self._processorMap["p2.hire.talent.tag_subscription_v1"] = P2HireTalentTagSubscriptionV1Processor(f)
		return self
	
	def register_p2_im_chat_disbanded_v1(self, f: Callable[[P2ImChatDisbandedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.disbanded_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.disbanded_v1")
		self._processorMap["p2.im.chat.disbanded_v1"] = P2ImChatDisbandedV1Processor(f)
		return self
	
	def register_p2_im_chat_updated_v1(self, f: Callable[[P2ImChatUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.updated_v1")
		self._processorMap["p2.im.chat.updated_v1"] = P2ImChatUpdatedV1Processor(f)
		return self
	
	def register_p2_im_chat_access_event_bot_p2p_chat_entered_v1(self, f: Callable[[P2ImChatAccessEventBotP2pChatEnteredV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.access_event.bot_p2p_chat_entered_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.access_event.bot_p2p_chat_entered_v1")
		self._processorMap["p2.im.chat.access_event.bot_p2p_chat_entered_v1"] = P2ImChatAccessEventBotP2pChatEnteredV1Processor(f)
		return self
	
	def register_p2_im_chat_member_bot_added_v1(self, f: Callable[[P2ImChatMemberBotAddedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.member.bot.added_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.member.bot.added_v1")
		self._processorMap["p2.im.chat.member.bot.added_v1"] = P2ImChatMemberBotAddedV1Processor(f)
		return self
	
	def register_p2_im_chat_member_bot_deleted_v1(self, f: Callable[[P2ImChatMemberBotDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.member.bot.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.member.bot.deleted_v1")
		self._processorMap["p2.im.chat.member.bot.deleted_v1"] = P2ImChatMemberBotDeletedV1Processor(f)
		return self
	
	def register_p2_im_chat_member_user_added_v1(self, f: Callable[[P2ImChatMemberUserAddedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.member.user.added_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.member.user.added_v1")
		self._processorMap["p2.im.chat.member.user.added_v1"] = P2ImChatMemberUserAddedV1Processor(f)
		return self
	
	def register_p2_im_chat_member_user_deleted_v1(self, f: Callable[[P2ImChatMemberUserDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.member.user.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.member.user.deleted_v1")
		self._processorMap["p2.im.chat.member.user.deleted_v1"] = P2ImChatMemberUserDeletedV1Processor(f)
		return self
	
	def register_p2_im_chat_member_user_withdrawn_v1(self, f: Callable[[P2ImChatMemberUserWithdrawnV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.chat.member.user.withdrawn_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.chat.member.user.withdrawn_v1")
		self._processorMap["p2.im.chat.member.user.withdrawn_v1"] = P2ImChatMemberUserWithdrawnV1Processor(f)
		return self
	
	def register_p2_im_message_message_read_v1(self, f: Callable[[P2ImMessageMessageReadV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.message.message_read_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.message.message_read_v1")
		self._processorMap["p2.im.message.message_read_v1"] = P2ImMessageMessageReadV1Processor(f)
		return self
	
	def register_p2_im_message_recalled_v1(self, f: Callable[[P2ImMessageRecalledV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.message.recalled_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.message.recalled_v1")
		self._processorMap["p2.im.message.recalled_v1"] = P2ImMessageRecalledV1Processor(f)
		return self
	
	def register_p2_im_message_receive_v1(self, f: Callable[[P2ImMessageReceiveV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.message.receive_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.message.receive_v1")
		self._processorMap["p2.im.message.receive_v1"] = P2ImMessageReceiveV1Processor(f)
		return self
	
	def register_p2_im_message_reaction_created_v1(self, f: Callable[[P2ImMessageReactionCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.message.reaction.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.message.reaction.created_v1")
		self._processorMap["p2.im.message.reaction.created_v1"] = P2ImMessageReactionCreatedV1Processor(f)
		return self
	
	def register_p2_im_message_reaction_deleted_v1(self, f: Callable[[P2ImMessageReactionDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.im.message.reaction.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.im.message.reaction.deleted_v1")
		self._processorMap["p2.im.message.reaction.deleted_v1"] = P2ImMessageReactionDeletedV1Processor(f)
		return self
	
	def register_p2_mail_user_mailbox_event_message_received_v1(self, f: Callable[[P2MailUserMailboxEventMessageReceivedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.mail.user_mailbox.event.message_received_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.mail.user_mailbox.event.message_received_v1")
		self._processorMap["p2.mail.user_mailbox.event.message_received_v1"] = P2MailUserMailboxEventMessageReceivedV1Processor(f)
		return self
	
	def register_p2_meeting_room_meeting_room_created_v1(self, f: Callable[[P2MeetingRoomMeetingRoomCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.meeting_room.meeting_room.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.meeting_room.meeting_room.created_v1")
		self._processorMap["p2.meeting_room.meeting_room.created_v1"] = P2MeetingRoomMeetingRoomCreatedV1Processor(f)
		return self
	
	def register_p2_meeting_room_meeting_room_deleted_v1(self, f: Callable[[P2MeetingRoomMeetingRoomDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.meeting_room.meeting_room.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.meeting_room.meeting_room.deleted_v1")
		self._processorMap["p2.meeting_room.meeting_room.deleted_v1"] = P2MeetingRoomMeetingRoomDeletedV1Processor(f)
		return self
	
	def register_p2_meeting_room_meeting_room_status_changed_v1(self, f: Callable[[P2MeetingRoomMeetingRoomStatusChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.meeting_room.meeting_room.status_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.meeting_room.meeting_room.status_changed_v1")
		self._processorMap["p2.meeting_room.meeting_room.status_changed_v1"] = P2MeetingRoomMeetingRoomStatusChangedV1Processor(f)
		return self
	
	def register_p2_meeting_room_meeting_room_updated_v1(self, f: Callable[[P2MeetingRoomMeetingRoomUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.meeting_room.meeting_room.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.meeting_room.meeting_room.updated_v1")
		self._processorMap["p2.meeting_room.meeting_room.updated_v1"] = P2MeetingRoomMeetingRoomUpdatedV1Processor(f)
		return self
	
	def register_p2_moments_comment_created_v1(self, f: Callable[[P2MomentsCommentCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.comment.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.comment.created_v1")
		self._processorMap["p2.moments.comment.created_v1"] = P2MomentsCommentCreatedV1Processor(f)
		return self
	
	def register_p2_moments_comment_deleted_v1(self, f: Callable[[P2MomentsCommentDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.comment.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.comment.deleted_v1")
		self._processorMap["p2.moments.comment.deleted_v1"] = P2MomentsCommentDeletedV1Processor(f)
		return self
	
	def register_p2_moments_post_created_v1(self, f: Callable[[P2MomentsPostCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.post.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.post.created_v1")
		self._processorMap["p2.moments.post.created_v1"] = P2MomentsPostCreatedV1Processor(f)
		return self
	
	def register_p2_moments_post_deleted_v1(self, f: Callable[[P2MomentsPostDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.post.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.post.deleted_v1")
		self._processorMap["p2.moments.post.deleted_v1"] = P2MomentsPostDeletedV1Processor(f)
		return self
	
	def register_p2_moments_post_statistics_updated_v1(self, f: Callable[[P2MomentsPostStatisticsUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.post_statistics.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.post_statistics.updated_v1")
		self._processorMap["p2.moments.post_statistics.updated_v1"] = P2MomentsPostStatisticsUpdatedV1Processor(f)
		return self
	
	def register_p2_moments_reaction_created_v1(self, f: Callable[[P2MomentsReactionCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.reaction.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.reaction.created_v1")
		self._processorMap["p2.moments.reaction.created_v1"] = P2MomentsReactionCreatedV1Processor(f)
		return self
	
	def register_p2_moments_reaction_deleted_v1(self, f: Callable[[P2MomentsReactionDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.moments.reaction.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.moments.reaction.deleted_v1")
		self._processorMap["p2.moments.reaction.deleted_v1"] = P2MomentsReactionDeletedV1Processor(f)
		return self
	
	def register_p2_payroll_payment_activity_approved_v1(self, f: Callable[[P2PayrollPaymentActivityApprovedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.payroll.payment_activity.approved_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.payroll.payment_activity.approved_v1")
		self._processorMap["p2.payroll.payment_activity.approved_v1"] = P2PayrollPaymentActivityApprovedV1Processor(f)
		return self
	
	def register_p2_payroll_payment_activity_status_changed_v1(self, f: Callable[[P2PayrollPaymentActivityStatusChangedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.payroll.payment_activity.status_changed_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.payroll.payment_activity.status_changed_v1")
		self._processorMap["p2.payroll.payment_activity.status_changed_v1"] = P2PayrollPaymentActivityStatusChangedV1Processor(f)
		return self
	
	def register_p2_performance_review_data_changed_v2(self, f: Callable[[P2PerformanceReviewDataChangedV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.performance.review_data.changed_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.performance.review_data.changed_v2")
		self._processorMap["p2.performance.review_data.changed_v2"] = P2PerformanceReviewDataChangedV2Processor(f)
		return self
	
	def register_p2_performance_stage_task_open_result_v2(self, f: Callable[[P2PerformanceStageTaskOpenResultV2], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.performance.stage_task.open_result_v2" in self._processorMap:
			raise EventException("processor already registered, type: p2.performance.stage_task.open_result_v2")
		self._processorMap["p2.performance.stage_task.open_result_v2"] = P2PerformanceStageTaskOpenResultV2Processor(f)
		return self
	
	def register_p2_task_task_update_tenant_v1(self, f: Callable[[P2TaskTaskUpdateTenantV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.task.task.update_tenant_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.task.task.update_tenant_v1")
		self._processorMap["p2.task.task.update_tenant_v1"] = P2TaskTaskUpdateTenantV1Processor(f)
		return self
	
	def register_p2_task_task_updated_v1(self, f: Callable[[P2TaskTaskUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.task.task.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.task.task.updated_v1")
		self._processorMap["p2.task.task.updated_v1"] = P2TaskTaskUpdatedV1Processor(f)
		return self
	
	def register_p2_task_task_comment_updated_v1(self, f: Callable[[P2TaskTaskCommentUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.task.task.comment.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.task.task.comment.updated_v1")
		self._processorMap["p2.task.task.comment.updated_v1"] = P2TaskTaskCommentUpdatedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_all_meeting_ended_v1(self, f: Callable[[P2VcMeetingAllMeetingEndedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.all_meeting_ended_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.all_meeting_ended_v1")
		self._processorMap["p2.vc.meeting.all_meeting_ended_v1"] = P2VcMeetingAllMeetingEndedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_all_meeting_started_v1(self, f: Callable[[P2VcMeetingAllMeetingStartedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.all_meeting_started_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.all_meeting_started_v1")
		self._processorMap["p2.vc.meeting.all_meeting_started_v1"] = P2VcMeetingAllMeetingStartedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_join_meeting_v1(self, f: Callable[[P2VcMeetingJoinMeetingV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.join_meeting_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.join_meeting_v1")
		self._processorMap["p2.vc.meeting.join_meeting_v1"] = P2VcMeetingJoinMeetingV1Processor(f)
		return self
	
	def register_p2_vc_meeting_leave_meeting_v1(self, f: Callable[[P2VcMeetingLeaveMeetingV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.leave_meeting_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.leave_meeting_v1")
		self._processorMap["p2.vc.meeting.leave_meeting_v1"] = P2VcMeetingLeaveMeetingV1Processor(f)
		return self
	
	def register_p2_vc_meeting_meeting_ended_v1(self, f: Callable[[P2VcMeetingMeetingEndedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.meeting_ended_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.meeting_ended_v1")
		self._processorMap["p2.vc.meeting.meeting_ended_v1"] = P2VcMeetingMeetingEndedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_meeting_started_v1(self, f: Callable[[P2VcMeetingMeetingStartedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.meeting_started_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.meeting_started_v1")
		self._processorMap["p2.vc.meeting.meeting_started_v1"] = P2VcMeetingMeetingStartedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_recording_ended_v1(self, f: Callable[[P2VcMeetingRecordingEndedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.recording_ended_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.recording_ended_v1")
		self._processorMap["p2.vc.meeting.recording_ended_v1"] = P2VcMeetingRecordingEndedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_recording_ready_v1(self, f: Callable[[P2VcMeetingRecordingReadyV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.recording_ready_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.recording_ready_v1")
		self._processorMap["p2.vc.meeting.recording_ready_v1"] = P2VcMeetingRecordingReadyV1Processor(f)
		return self
	
	def register_p2_vc_meeting_recording_started_v1(self, f: Callable[[P2VcMeetingRecordingStartedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.recording_started_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.recording_started_v1")
		self._processorMap["p2.vc.meeting.recording_started_v1"] = P2VcMeetingRecordingStartedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_share_ended_v1(self, f: Callable[[P2VcMeetingShareEndedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.share_ended_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.share_ended_v1")
		self._processorMap["p2.vc.meeting.share_ended_v1"] = P2VcMeetingShareEndedV1Processor(f)
		return self
	
	def register_p2_vc_meeting_share_started_v1(self, f: Callable[[P2VcMeetingShareStartedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.meeting.share_started_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.meeting.share_started_v1")
		self._processorMap["p2.vc.meeting.share_started_v1"] = P2VcMeetingShareStartedV1Processor(f)
		return self
	
	def register_p2_vc_reserve_config_updated_v1(self, f: Callable[[P2VcReserveConfigUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.reserve_config.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.reserve_config.updated_v1")
		self._processorMap["p2.vc.reserve_config.updated_v1"] = P2VcReserveConfigUpdatedV1Processor(f)
		return self
	
	def register_p2_vc_room_created_v1(self, f: Callable[[P2VcRoomCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room.created_v1")
		self._processorMap["p2.vc.room.created_v1"] = P2VcRoomCreatedV1Processor(f)
		return self
	
	def register_p2_vc_room_deleted_v1(self, f: Callable[[P2VcRoomDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room.deleted_v1")
		self._processorMap["p2.vc.room.deleted_v1"] = P2VcRoomDeletedV1Processor(f)
		return self
	
	def register_p2_vc_room_updated_v1(self, f: Callable[[P2VcRoomUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room.updated_v1")
		self._processorMap["p2.vc.room.updated_v1"] = P2VcRoomUpdatedV1Processor(f)
		return self
	
	def register_p2_vc_room_level_created_v1(self, f: Callable[[P2VcRoomLevelCreatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room_level.created_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room_level.created_v1")
		self._processorMap["p2.vc.room_level.created_v1"] = P2VcRoomLevelCreatedV1Processor(f)
		return self
	
	def register_p2_vc_room_level_deleted_v1(self, f: Callable[[P2VcRoomLevelDeletedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room_level.deleted_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room_level.deleted_v1")
		self._processorMap["p2.vc.room_level.deleted_v1"] = P2VcRoomLevelDeletedV1Processor(f)
		return self
	
	def register_p2_vc_room_level_updated_v1(self, f: Callable[[P2VcRoomLevelUpdatedV1], None]) -> "EventDispatcherHandlerBuilder":
		if "p2.vc.room_level.updated_v1" in self._processorMap:
			raise EventException("processor already registered, type: p2.vc.room_level.updated_v1")
		self._processorMap["p2.vc.room_level.updated_v1"] = P2VcRoomLevelUpdatedV1Processor(f)
		return self
	

	def build(self) -> EventDispatcherHandler:
		event_dispatcher_handler = EventDispatcherHandler()
		event_dispatcher_handler._encrypt_key = self._encrypt_key
		event_dispatcher_handler._verification_token = self._verification_token
		event_dispatcher_handler._processorMap = self._processorMap
		event_dispatcher_handler._callback_processor_map = self._callback_processor_map
		return event_dispatcher_handler