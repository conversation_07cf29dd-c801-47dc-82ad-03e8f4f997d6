# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .key_point_match_details import KeyPointMatchDetails


class SubjectiveCheckOutput(object):
    _types = {
        "keypoints": List[str],
        "key_point_match_details": List[KeyPointMatchDetails],
        "user_prompt_outputs": List[str],
        "status_code": int,
    }

    def __init__(self, d=None):
        self.keypoints: Optional[List[str]] = None
        self.key_point_match_details: Optional[List[KeyPointMatchDetails]] = None
        self.user_prompt_outputs: Optional[List[str]] = None
        self.status_code: Optional[int] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SubjectiveCheckOutputBuilder":
        return SubjectiveCheckOutputBuilder()


class SubjectiveCheckOutputBuilder(object):
    def __init__(self) -> None:
        self._subjective_check_output = SubjectiveCheckOutput()
    def keypoints(self, keypoints: List[str]) -> "SubjectiveCheckOutputBuilder":
        self._subjective_check_output.keypoints = keypoints
        return self
    def key_point_match_details(self, key_point_match_details: List[KeyPointMatchDetails]) -> "SubjectiveCheckOutputBuilder":
        self._subjective_check_output.key_point_match_details = key_point_match_details
        return self
    def user_prompt_outputs(self, user_prompt_outputs: List[str]) -> "SubjectiveCheckOutputBuilder":
        self._subjective_check_output.user_prompt_outputs = user_prompt_outputs
        return self
    def status_code(self, status_code: int) -> "SubjectiveCheckOutputBuilder":
        self._subjective_check_output.status_code = status_code
        return self
    
    def build(self) -> "SubjectiveCheckOutput":
        return self._subjective_check_output