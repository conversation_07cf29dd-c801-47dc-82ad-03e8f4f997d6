# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SubscribeDepartment(object):
    _types = {
        "department_id": int,
        "department_name": str,
    }

    def __init__(self, d=None):
        self.department_id: Optional[int] = None
        self.department_name: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SubscribeDepartmentBuilder":
        return SubscribeDepartmentBuilder()


class SubscribeDepartmentBuilder(object):
    def __init__(self) -> None:
        self._subscribe_department = SubscribeDepartment()
    def department_id(self, department_id: int) -> "SubscribeDepartmentBuilder":
        self._subscribe_department.department_id = department_id
        return self
    def department_name(self, department_name: str) -> "SubscribeDepartmentBuilder":
        self._subscribe_department.department_name = department_name
        return self
    
    def build(self) -> "SubscribeDepartment":
        return self._subscribe_department