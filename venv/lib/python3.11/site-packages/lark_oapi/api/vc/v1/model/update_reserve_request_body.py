# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .reserve_meeting_setting import ReserveMeetingSetting


class UpdateReserveRequestBody(object):
    _types = {
        "end_time": int,
        "meeting_settings": ReserveMeetingSetting,
    }

    def __init__(self, d=None):
        self.end_time: Optional[int] = None
        self.meeting_settings: Optional[ReserveMeetingSetting] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "UpdateReserveRequestBodyBuilder":
        return UpdateReserveRequestBodyBuilder()


class UpdateReserveRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._update_reserve_request_body = UpdateReserveRequestBody()
    def end_time(self, end_time: int) -> "UpdateReserveRequestBodyBuilder":
        self._update_reserve_request_body.end_time = end_time
        return self
    def meeting_settings(self, meeting_settings: ReserveMeetingSetting) -> "UpdateReserveRequestBodyBuilder":
        self._update_reserve_request_body.meeting_settings = meeting_settings
        return self
    
    def build(self) -> "UpdateReserveRequestBody":
        return self._update_reserve_request_body