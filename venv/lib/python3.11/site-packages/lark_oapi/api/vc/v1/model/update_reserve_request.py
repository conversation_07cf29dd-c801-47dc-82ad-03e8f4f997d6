# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .update_reserve_request_body import UpdateReserveRequestBody


class UpdateReserveRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.user_id_type: Optional[str] = None
        self.reserve_id: Optional[int] = None
        self.request_body: Optional[UpdateReserveRequestBody] = None

    @staticmethod
    def builder() -> "UpdateReserveRequestBuilder":
        return UpdateReserveRequestBuilder()


class UpdateReserveRequestBuilder(object):

    def __init__(self) -> None:
        update_reserve_request = UpdateReserveRequest()
        update_reserve_request.http_method = HttpMethod.PUT
        update_reserve_request.uri = "/open-apis/vc/v1/reserves/:reserve_id"
        update_reserve_request.token_types = {AccessTokenType.USER, AccessTokenType.TENANT}
        self._update_reserve_request: UpdateReserveRequest = update_reserve_request
    
    def user_id_type(self, user_id_type: str) -> "UpdateReserveRequestBuilder":
        self._update_reserve_request.user_id_type = user_id_type
        self._update_reserve_request.add_query("user_id_type", user_id_type)
        return self
    
    def reserve_id(self, reserve_id: int) -> "UpdateReserveRequestBuilder":
        self._update_reserve_request.reserve_id = reserve_id
        self._update_reserve_request.paths["reserve_id"] = str(reserve_id)
        return self
    
    def request_body(self, request_body: UpdateReserveRequestBody) -> "UpdateReserveRequestBuilder":
        self._update_reserve_request.request_body = request_body
        self._update_reserve_request.body = request_body
        return self

    def build(self) -> UpdateReserveRequest:
        return self._update_reserve_request
