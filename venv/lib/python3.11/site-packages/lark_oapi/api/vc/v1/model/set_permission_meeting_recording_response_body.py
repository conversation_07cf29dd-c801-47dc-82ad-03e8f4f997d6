# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SetPermissionMeetingRecordingResponseBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SetPermissionMeetingRecordingResponseBodyBuilder":
        return SetPermissionMeetingRecordingResponseBodyBuilder()


class SetPermissionMeetingRecordingResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._set_permission_meeting_recording_response_body = SetPermissionMeetingRecordingResponseBody()

    def build(self) -> "SetPermissionMeetingRecordingResponseBody":
        return self._set_permission_meeting_recording_response_body
