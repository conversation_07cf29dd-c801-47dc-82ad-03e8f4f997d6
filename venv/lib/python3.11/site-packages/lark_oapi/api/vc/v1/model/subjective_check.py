# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .key_point import KeyPoint


class SubjectiveCheck(object):
    _types = {
        "key_points": List[KeyPoint],
        "user_input_prompts": List[str],
    }

    def __init__(self, d=None):
        self.key_points: Optional[List[KeyPoint]] = None
        self.user_input_prompts: Optional[List[str]] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SubjectiveCheckBuilder":
        return SubjectiveCheckBuilder()


class SubjectiveCheckBuilder(object):
    def __init__(self) -> None:
        self._subjective_check = SubjectiveCheck()
    def key_points(self, key_points: List[KeyPoint]) -> "SubjectiveCheckBuilder":
        self._subjective_check.key_points = key_points
        return self
    def user_input_prompts(self, user_input_prompts: List[str]) -> "SubjectiveCheckBuilder":
        self._subjective_check.user_input_prompts = user_input_prompts
        return self
    
    def build(self) -> "SubjectiveCheck":
        return self._subjective_check