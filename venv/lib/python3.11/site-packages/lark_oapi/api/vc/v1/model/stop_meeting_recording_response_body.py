# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class StopMeetingRecordingResponseBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "StopMeetingRecordingResponseBodyBuilder":
        return StopMeetingRecordingResponseBodyBuilder()


class StopMeetingRecordingResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._stop_meeting_recording_response_body = StopMeetingRecordingResponseBody()

    def build(self) -> "StopMeetingRecordingResponseBody":
        return self._stop_meeting_recording_response_body
