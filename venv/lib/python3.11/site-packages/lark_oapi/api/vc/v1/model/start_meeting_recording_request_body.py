# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class StartMeetingRecordingRequestBody(object):
    _types = {
        "timezone": int,
    }

    def __init__(self, d=None):
        self.timezone: Optional[int] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "StartMeetingRecordingRequestBodyBuilder":
        return StartMeetingRecordingRequestBodyBuilder()


class StartMeetingRecordingRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._start_meeting_recording_request_body = StartMeetingRecordingRequestBody()
    def timezone(self, timezone: int) -> "StartMeetingRecordingRequestBodyBuilder":
        self._start_meeting_recording_request_body.timezone = timezone
        return self
    
    def build(self) -> "StartMeetingRecordingRequestBody":
        return self._start_meeting_recording_request_body