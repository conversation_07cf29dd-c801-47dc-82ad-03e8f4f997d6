# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .reserve import Reserve
from .reserve_correction_check_info import ReserveCorrectionCheckInfo


class UpdateReserveResponseBody(object):
    _types = {
        "reserve": Reserve,
        "reserve_correction_check_info": ReserveCorrectionCheckInfo,
    }

    def __init__(self, d=None):
        self.reserve: Optional[Reserve] = None
        self.reserve_correction_check_info: Optional[ReserveCorrectionCheckInfo] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "UpdateReserveResponseBodyBuilder":
        return UpdateReserveResponseBodyBuilder()


class UpdateReserveResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._update_reserve_response_body = UpdateReserveResponseBody()
    def reserve(self, reserve: Reserve) -> "UpdateReserveResponseBodyBuilder":
        self._update_reserve_response_body.reserve = reserve
        return self
    def reserve_correction_check_info(self, reserve_correction_check_info: ReserveCorrectionCheckInfo) -> "UpdateReserveResponseBodyBuilder":
        self._update_reserve_response_body.reserve_correction_check_info = reserve_correction_check_info
        return self
    
    def build(self) -> "UpdateReserveResponseBody":
        return self._update_reserve_response_body