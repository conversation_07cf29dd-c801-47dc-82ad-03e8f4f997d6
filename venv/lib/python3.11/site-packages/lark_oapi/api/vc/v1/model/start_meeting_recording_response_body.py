# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class StartMeetingRecordingResponseBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "StartMeetingRecordingResponseBodyBuilder":
        return StartMeetingRecordingResponseBodyBuilder()


class StartMeetingRecordingResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._start_meeting_recording_response_body = StartMeetingRecordingResponseBody()

    def build(self) -> "StartMeetingRecordingResponseBody":
        return self._start_meeting_recording_response_body
