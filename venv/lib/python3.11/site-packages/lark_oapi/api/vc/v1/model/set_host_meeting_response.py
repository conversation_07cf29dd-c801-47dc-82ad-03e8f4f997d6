# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from lark_oapi.core.model import BaseResponse
from .set_host_meeting_response_body import SetHostMeetingResponseBody


class SetHostMeetingResponse(BaseResponse):
    _types = {
        "data": SetHostMeetingResponseBody
    }

    def __init__(self, d=None):
        super().__init__(d)
        self.data: Optional[SetHostMeetingResponseBody] = None
        init(self, d, self._types)
