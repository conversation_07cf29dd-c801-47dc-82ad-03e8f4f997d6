# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .recording_permission_object import RecordingPermissionObject


class SetPermissionMeetingRecordingRequestBody(object):
    _types = {
        "permission_objects": List[RecordingPermissionObject],
        "action_type": int,
    }

    def __init__(self, d=None):
        self.permission_objects: Optional[List[RecordingPermissionObject]] = None
        self.action_type: Optional[int] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SetPermissionMeetingRecordingRequestBodyBuilder":
        return SetPermissionMeetingRecordingRequestBodyBuilder()


class SetPermissionMeetingRecordingRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._set_permission_meeting_recording_request_body = SetPermissionMeetingRecordingRequestBody()
    def permission_objects(self, permission_objects: List[RecordingPermissionObject]) -> "SetPermissionMeetingRecordingRequestBodyBuilder":
        self._set_permission_meeting_recording_request_body.permission_objects = permission_objects
        return self
    def action_type(self, action_type: int) -> "SetPermissionMeetingRecordingRequestBodyBuilder":
        self._set_permission_meeting_recording_request_body.action_type = action_type
        return self
    
    def build(self) -> "SetPermissionMeetingRecordingRequestBody":
        return self._set_permission_meeting_recording_request_body