# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class UserId(object):
    _types = {
        "user_id": str,
        "open_id": str,
        "union_id": str,
    }

    def __init__(self, d=None):
        self.user_id: Optional[str] = None
        self.open_id: Optional[str] = None
        self.union_id: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "UserIdBuilder":
        return UserIdBuilder()


class UserIdBuilder(object):
    def __init__(self) -> None:
        self._user_id = UserId()
    def user_id(self, user_id: str) -> "UserIdBuilder":
        self._user_id.user_id = user_id
        return self
    def open_id(self, open_id: str) -> "UserIdBuilder":
        self._user_id.open_id = open_id
        return self
    def union_id(self, union_id: str) -> "UserIdBuilder":
        self._user_id.union_id = union_id
        return self
    
    def build(self) -> "UserId":
        return self._user_id