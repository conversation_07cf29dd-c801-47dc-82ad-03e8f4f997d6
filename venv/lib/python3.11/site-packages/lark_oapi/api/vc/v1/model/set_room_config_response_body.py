# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SetRoomConfigResponseBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SetRoomConfigResponseBodyBuilder":
        return SetRoomConfigResponseBodyBuilder()


class SetRoomConfigResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._set_room_config_response_body = SetRoomConfigResponseBody()

    def build(self) -> "SetRoomConfigResponseBody":
        return self._set_room_config_response_body
