# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .user_id import UserId


class SubscribeUserEvent(object):
    _types = {
        "user_id": UserId,
    }

    def __init__(self, d=None):
        self.user_id: Optional[UserId] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SubscribeUserEventBuilder":
        return SubscribeUserEventBuilder()


class SubscribeUserEventBuilder(object):
    def __init__(self) -> None:
        self._subscribe_user_event = SubscribeUserEvent()
    def user_id(self, user_id: UserId) -> "SubscribeUserEventBuilder":
        self._subscribe_user_event.user_id = user_id
        return self
    
    def build(self) -> "SubscribeUserEvent":
        return self._subscribe_user_event