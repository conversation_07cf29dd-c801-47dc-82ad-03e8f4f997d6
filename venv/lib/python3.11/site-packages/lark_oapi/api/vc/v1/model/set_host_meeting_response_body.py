# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .meeting_user import MeetingUser


class SetHostMeetingResponseBody(object):
    _types = {
        "host_user": MeetingUser,
    }

    def __init__(self, d=None):
        self.host_user: Optional[MeetingUser] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SetHostMeetingResponseBodyBuilder":
        return SetHostMeetingResponseBodyBuilder()


class SetHostMeetingResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._set_host_meeting_response_body = SetHostMeetingResponseBody()
    def host_user(self, host_user: MeetingUser) -> "SetHostMeetingResponseBodyBuilder":
        self._set_host_meeting_response_body.host_user = host_user
        return self
    
    def build(self) -> "SetHostMeetingResponseBody":
        return self._set_host_meeting_response_body