# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .meeting_user import MeetingUser
from .meeting_user import MeetingUser


class SetHostMeetingRequestBody(object):
    _types = {
        "host_user": MeetingUser,
        "old_host_user": MeetingUser,
    }

    def __init__(self, d=None):
        self.host_user: Optional[MeetingUser] = None
        self.old_host_user: Optional[MeetingUser] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SetHostMeetingRequestBodyBuilder":
        return SetHostMeetingRequestBodyBuilder()


class SetHostMeetingRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._set_host_meeting_request_body = SetHostMeetingRequestBody()
    def host_user(self, host_user: MeetingUser) -> "SetHostMeetingRequestBodyBuilder":
        self._set_host_meeting_request_body.host_user = host_user
        return self
    def old_host_user(self, old_host_user: MeetingUser) -> "SetHostMeetingRequestBodyBuilder":
        self._set_host_meeting_request_body.old_host_user = old_host_user
        return self
    
    def build(self) -> "SetHostMeetingRequestBody":
        return self._set_host_meeting_request_body