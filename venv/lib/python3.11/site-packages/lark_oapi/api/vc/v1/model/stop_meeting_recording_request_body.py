# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class StopMeetingRecordingRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "StopMeetingRecordingRequestBodyBuilder":
        return StopMeetingRecordingRequestBodyBuilder()


class StopMeetingRecordingRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._stop_meeting_recording_request_body = StopMeetingRecordingRequestBody()

    def build(self) -> "StopMeetingRecordingRequestBody":
        return self._stop_meeting_recording_request_body
