# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class Rule(object):
    _types = {
        "is_all_visible": bool,
        "visible_department_ids": List[str],
    }

    def __init__(self, d=None):
        self.is_all_visible: Optional[bool] = None
        self.visible_department_ids: Optional[List[str]] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "RuleBuilder":
        return RuleBuilder()


class RuleBuilder(object):
    def __init__(self) -> None:
        self._rule = Rule()
    def is_all_visible(self, is_all_visible: bool) -> "RuleBuilder":
        self._rule.is_all_visible = is_all_visible
        return self
    def visible_department_ids(self, visible_department_ids: List[str]) -> "RuleBuilder":
        self._rule.visible_department_ids = visible_department_ids
        return self
    
    def build(self) -> "Rule":
        return self._rule