# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SearchWorkplaceBlockAccessDataRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchWorkplaceBlockAccessDataRequestBodyBuilder":
        return SearchWorkplaceBlockAccessDataRequestBodyBuilder()


class SearchWorkplaceBlockAccessDataRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._search_workplace_block_access_data_request_body = SearchWorkplaceBlockAccessDataRequestBody()

    def build(self) -> "SearchWorkplaceBlockAccessDataRequestBody":
        return self._search_workplace_block_access_data_request_body
