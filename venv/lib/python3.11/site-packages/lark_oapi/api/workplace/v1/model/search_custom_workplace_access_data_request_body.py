# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SearchCustomWorkplaceAccessDataRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchCustomWorkplaceAccessDataRequestBodyBuilder":
        return SearchCustomWorkplaceAccessDataRequestBodyBuilder()


class SearchCustomWorkplaceAccessDataRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._search_custom_workplace_access_data_request_body = SearchCustomWorkplaceAccessDataRequestBody()

    def build(self) -> "SearchCustomWorkplaceAccessDataRequestBody":
        return self._search_custom_workplace_access_data_request_body
