# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SearchWorkplaceAccessDataRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchWorkplaceAccessDataRequestBodyBuilder":
        return SearchWorkplaceAccessDataRequestBodyBuilder()


class SearchWorkplaceAccessDataRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._search_workplace_access_data_request_body = SearchWorkplaceAccessDataRequestBody()

    def build(self) -> "SearchWorkplaceAccessDataRequestBody":
        return self._search_workplace_access_data_request_body
