# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .access_data import AccessData
from .access_data import AccessData


class WorkplaceAccessData(object):
    _types = {
        "date": str,
        "all_workplace": AccessData,
        "default_workplace": AccessData,
    }

    def __init__(self, d=None):
        self.date: Optional[str] = None
        self.all_workplace: Optional[AccessData] = None
        self.default_workplace: Optional[AccessData] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "WorkplaceAccessDataBuilder":
        return WorkplaceAccessDataBuilder()


class WorkplaceAccessDataBuilder(object):
    def __init__(self) -> None:
        self._workplace_access_data = WorkplaceAccessData()
    def date(self, date: str) -> "WorkplaceAccessDataBuilder":
        self._workplace_access_data.date = date
        return self
    def all_workplace(self, all_workplace: AccessData) -> "WorkplaceAccessDataBuilder":
        self._workplace_access_data.all_workplace = all_workplace
        return self
    def default_workplace(self, default_workplace: AccessData) -> "WorkplaceAccessDataBuilder":
        self._workplace_access_data.default_workplace = default_workplace
        return self
    
    def build(self) -> "WorkplaceAccessData":
        return self._workplace_access_data