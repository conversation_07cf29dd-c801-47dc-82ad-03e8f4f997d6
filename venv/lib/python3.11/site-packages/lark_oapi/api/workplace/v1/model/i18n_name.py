# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class I18nName(object):
    _types = {
        "language": str,
        "name": str,
    }

    def __init__(self, d=None):
        self.language: Optional[str] = None
        self.name: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "I18nNameBuilder":
        return I18nNameBuilder()


class I18nNameBuilder(object):
    def __init__(self) -> None:
        self._i18n_name = I18nName()
    def language(self, language: str) -> "I18nNameBuilder":
        self._i18n_name.language = language
        return self
    def name(self, name: str) -> "I18nNameBuilder":
        self._i18n_name.name = name
        return self
    
    def build(self) -> "I18nName":
        return self._i18n_name