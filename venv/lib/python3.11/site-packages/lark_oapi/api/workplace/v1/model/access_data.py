# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class AccessData(object):
    _types = {
        "pv": int,
        "uv": int,
    }

    def __init__(self, d=None):
        self.pv: Optional[int] = None
        self.uv: Optional[int] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "AccessDataBuilder":
        return AccessDataBuilder()


class AccessDataBuilder(object):
    def __init__(self) -> None:
        self._access_data = AccessData()
    def pv(self, pv: int) -> "AccessDataBuilder":
        self._access_data.pv = pv
        return self
    def uv(self, uv: int) -> "AccessDataBuilder":
        self._access_data.uv = uv
        return self
    
    def build(self) -> "AccessData":
        return self._access_data