# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .block_access_data import BlockAccessData


class SearchWorkplaceBlockAccessDataResponseBody(object):
    _types = {
        "items": List[BlockAccessData],
        "has_more": bool,
        "page_token": str,
    }

    def __init__(self, d=None):
        self.items: Optional[List[BlockAccessData]] = None
        self.has_more: Optional[bool] = None
        self.page_token: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchWorkplaceBlockAccessDataResponseBodyBuilder":
        return SearchWorkplaceBlockAccessDataResponseBodyBuilder()


class SearchWorkplaceBlockAccessDataResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._search_workplace_block_access_data_response_body = SearchWorkplaceBlockAccessDataResponseBody()
    def items(self, items: List[BlockAccessData]) -> "SearchWorkplaceBlockAccessDataResponseBodyBuilder":
        self._search_workplace_block_access_data_response_body.items = items
        return self
    def has_more(self, has_more: bool) -> "SearchWorkplaceBlockAccessDataResponseBodyBuilder":
        self._search_workplace_block_access_data_response_body.has_more = has_more
        return self
    def page_token(self, page_token: str) -> "SearchWorkplaceBlockAccessDataResponseBodyBuilder":
        self._search_workplace_block_access_data_response_body.page_token = page_token
        return self
    
    def build(self) -> "SearchWorkplaceBlockAccessDataResponseBody":
        return self._search_workplace_block_access_data_response_body