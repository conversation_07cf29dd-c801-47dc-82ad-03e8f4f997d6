# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .access_data import AccessData
from .i18n_name import I18nName


class CustomWorkplaceAccessData(object):
    _types = {
        "custom_workplace_id": str,
        "access_data": AccessData,
        "date": str,
        "custom_workplace_name": List[I18nName],
    }

    def __init__(self, d=None):
        self.custom_workplace_id: Optional[str] = None
        self.access_data: Optional[AccessData] = None
        self.date: Optional[str] = None
        self.custom_workplace_name: Optional[List[I18nName]] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "CustomWorkplaceAccessDataBuilder":
        return CustomWorkplaceAccessDataBuilder()


class CustomWorkplaceAccessDataBuilder(object):
    def __init__(self) -> None:
        self._custom_workplace_access_data = CustomWorkplaceAccessData()
    def custom_workplace_id(self, custom_workplace_id: str) -> "CustomWorkplaceAccessDataBuilder":
        self._custom_workplace_access_data.custom_workplace_id = custom_workplace_id
        return self
    def access_data(self, access_data: AccessData) -> "CustomWorkplaceAccessDataBuilder":
        self._custom_workplace_access_data.access_data = access_data
        return self
    def date(self, date: str) -> "CustomWorkplaceAccessDataBuilder":
        self._custom_workplace_access_data.date = date
        return self
    def custom_workplace_name(self, custom_workplace_name: List[I18nName]) -> "CustomWorkplaceAccessDataBuilder":
        self._custom_workplace_access_data.custom_workplace_name = custom_workplace_name
        return self
    
    def build(self) -> "CustomWorkplaceAccessData":
        return self._custom_workplace_access_data