# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .task_result import TaskResult


class GetTaskResponseBody(object):
    _types = {
        "task": TaskResult,
    }

    def __init__(self, d=None):
        self.task: Optional[TaskResult] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "GetTaskResponseBodyBuilder":
        return GetTaskResponseBodyBuilder()


class GetTaskResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._get_task_response_body = GetTaskResponseBody()
    def task(self, task: TaskResult) -> "GetTaskResponseBodyBuilder":
        self._get_task_response_body.task = task
        return self
    
    def build(self) -> "GetTaskResponseBody":
        return self._get_task_response_body