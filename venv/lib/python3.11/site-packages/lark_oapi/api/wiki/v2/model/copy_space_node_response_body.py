# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .node import Node


class CopySpaceNodeResponseBody(object):
    _types = {
        "node": Node,
    }

    def __init__(self, d=None):
        self.node: Optional[Node] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "CopySpaceNodeResponseBodyBuilder":
        return CopySpaceNodeResponseBodyBuilder()


class CopySpaceNodeResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._copy_space_node_response_body = CopySpaceNodeResponseBody()
    def node(self, node: Node) -> "CopySpaceNodeResponseBodyBuilder":
        self._copy_space_node_response_body.node = node
        return self
    
    def build(self) -> "CopySpaceNodeResponseBody":
        return self._copy_space_node_response_body