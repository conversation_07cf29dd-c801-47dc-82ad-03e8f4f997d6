# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .node import Node


class CreateSpaceNodeRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.space_id: Optional[str] = None
        self.request_body: Optional[Node] = None

    @staticmethod
    def builder() -> "CreateSpaceNodeRequestBuilder":
        return CreateSpaceNodeRequestBuilder()


class CreateSpaceNodeRequestBuilder(object):

    def __init__(self) -> None:
        create_space_node_request = CreateSpaceNodeRequest()
        create_space_node_request.http_method = HttpMethod.POST
        create_space_node_request.uri = "/open-apis/wiki/v2/spaces/:space_id/nodes"
        create_space_node_request.token_types = {AccessTokenType.USER, AccessTokenType.TENANT}
        self._create_space_node_request: CreateSpaceNodeRequest = create_space_node_request
    
    def space_id(self, space_id: str) -> "CreateSpaceNodeRequestBuilder":
        self._create_space_node_request.space_id = space_id
        self._create_space_node_request.paths["space_id"] = str(space_id)
        return self
    
    def request_body(self, request_body: Node) -> "CreateSpaceNodeRequestBuilder":
        self._create_space_node_request.request_body = request_body
        self._create_space_node_request.body = request_body
        return self

    def build(self) -> CreateSpaceNodeRequest:
        return self._create_space_node_request
