# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .space import Space


class CreateSpaceRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.request_body: Optional[Space] = None

    @staticmethod
    def builder() -> "CreateSpaceRequestBuilder":
        return CreateSpaceRequestBuilder()


class CreateSpaceRequestBuilder(object):

    def __init__(self) -> None:
        create_space_request = CreateSpaceRequest()
        create_space_request.http_method = HttpMethod.POST
        create_space_request.uri = "/open-apis/wiki/v2/spaces"
        create_space_request.token_types = {AccessTokenType.USER}
        self._create_space_request: CreateSpaceRequest = create_space_request
    
    def request_body(self, request_body: Space) -> "CreateSpaceRequestBuilder":
        self._create_space_request.request_body = request_body
        self._create_space_request.body = request_body
        return self

    def build(self) -> CreateSpaceRequest:
        return self._create_space_request
