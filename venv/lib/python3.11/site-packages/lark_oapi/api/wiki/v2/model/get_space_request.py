# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType


class GetSpaceRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.lang: Optional[str] = None
        self.space_id: Optional[str] = None

    @staticmethod
    def builder() -> "GetSpaceRequestBuilder":
        return GetSpaceRequestBuilder()


class GetSpaceRequestBuilder(object):

    def __init__(self) -> None:
        get_space_request = GetSpaceRequest()
        get_space_request.http_method = HttpMethod.GET
        get_space_request.uri = "/open-apis/wiki/v2/spaces/:space_id"
        get_space_request.token_types = {AccessTokenType.USER, AccessTokenType.TENANT}
        self._get_space_request: GetSpaceRequest = get_space_request
    
    def lang(self, lang: str) -> "GetSpaceRequestBuilder":
        self._get_space_request.lang = lang
        self._get_space_request.add_query("lang", lang)
        return self
    
    def space_id(self, space_id: str) -> "GetSpaceRequestBuilder":
        self._get_space_request.space_id = space_id
        self._get_space_request.paths["space_id"] = str(space_id)
        return self
    

    def build(self) -> GetSpaceRequest:
        return self._get_space_request
