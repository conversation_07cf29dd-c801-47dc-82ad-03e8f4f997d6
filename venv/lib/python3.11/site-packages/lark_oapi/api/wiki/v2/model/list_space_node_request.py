# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType


class ListSpaceNodeRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.page_size: Optional[int] = None
        self.page_token: Optional[str] = None
        self.parent_node_token: Optional[str] = None
        self.space_id: Optional[str] = None

    @staticmethod
    def builder() -> "ListSpaceNodeRequestBuilder":
        return ListSpaceNodeRequestBuilder()


class ListSpaceNodeRequestBuilder(object):

    def __init__(self) -> None:
        list_space_node_request = ListSpaceNodeRequest()
        list_space_node_request.http_method = HttpMethod.GET
        list_space_node_request.uri = "/open-apis/wiki/v2/spaces/:space_id/nodes"
        list_space_node_request.token_types = {AccessTokenType.USER, AccessTokenType.TENANT}
        self._list_space_node_request: ListSpaceNodeRequest = list_space_node_request
    
    def page_size(self, page_size: int) -> "ListSpaceNodeRequestBuilder":
        self._list_space_node_request.page_size = page_size
        self._list_space_node_request.add_query("page_size", page_size)
        return self
    
    def page_token(self, page_token: str) -> "ListSpaceNodeRequestBuilder":
        self._list_space_node_request.page_token = page_token
        self._list_space_node_request.add_query("page_token", page_token)
        return self
    
    def parent_node_token(self, parent_node_token: str) -> "ListSpaceNodeRequestBuilder":
        self._list_space_node_request.parent_node_token = parent_node_token
        self._list_space_node_request.add_query("parent_node_token", parent_node_token)
        return self
    
    def space_id(self, space_id: str) -> "ListSpaceNodeRequestBuilder":
        self._list_space_node_request.space_id = space_id
        self._list_space_node_request.paths["space_id"] = str(space_id)
        return self
    

    def build(self) -> ListSpaceNodeRequest:
        return self._list_space_node_request
