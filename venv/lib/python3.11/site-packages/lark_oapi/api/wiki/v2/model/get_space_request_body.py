# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class GetSpaceRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "GetSpaceRequestBodyBuilder":
        return GetSpaceRequestBodyBuilder()


class GetSpaceRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._get_space_request_body = GetSpaceRequestBody()

    def build(self) -> "GetSpaceRequestBody":
        return self._get_space_request_body
