# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .setting import Setting


class UpdateSpaceSettingRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.space_id: Optional[str] = None
        self.request_body: Optional[Setting] = None

    @staticmethod
    def builder() -> "UpdateSpaceSettingRequestBuilder":
        return UpdateSpaceSettingRequestBuilder()


class UpdateSpaceSettingRequestBuilder(object):

    def __init__(self) -> None:
        update_space_setting_request = UpdateSpaceSettingRequest()
        update_space_setting_request.http_method = HttpMethod.PUT
        update_space_setting_request.uri = "/open-apis/wiki/v2/spaces/:space_id/setting"
        update_space_setting_request.token_types = {AccessTokenType.USER, AccessTokenType.TENANT}
        self._update_space_setting_request: UpdateSpaceSettingRequest = update_space_setting_request
    
    def space_id(self, space_id: str) -> "UpdateSpaceSettingRequestBuilder":
        self._update_space_setting_request.space_id = space_id
        self._update_space_setting_request.paths["space_id"] = str(space_id)
        return self
    
    def request_body(self, request_body: Setting) -> "UpdateSpaceSettingRequestBuilder":
        self._update_space_setting_request.request_body = request_body
        self._update_space_setting_request.body = request_body
        return self

    def build(self) -> UpdateSpaceSettingRequest:
        return self._update_space_setting_request
