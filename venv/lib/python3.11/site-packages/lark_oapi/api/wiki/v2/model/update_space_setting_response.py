# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from lark_oapi.core.model import BaseResponse
from .update_space_setting_response_body import UpdateSpaceSettingResponseBody


class UpdateSpaceSettingResponse(BaseResponse):
    _types = {
        "data": UpdateSpaceSettingResponseBody
    }

    def __init__(self, d=None):
        super().__init__(d)
        self.data: Optional[UpdateSpaceSettingResponseBody] = None
        init(self, d, self._types)
