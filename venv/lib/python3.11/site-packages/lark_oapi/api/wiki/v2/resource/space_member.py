# Code generated by Lark OpenAPI.

import io
from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.const import UTF_8, CONTENT_TYPE, APPLICATION_JSON
from lark_oapi.core import <PERSON><PERSON><PERSON>
from lark_oapi.core.token import verify
from lark_oapi.core.http import Transport
from lark_oapi.core.model import Config, RequestOption, RawResponse
from lark_oapi.core.utils import Files
from requests_toolbelt import MultipartEncoder
from ..model.create_space_member_request import CreateSpaceMemberRequest
from ..model.create_space_member_response import CreateSpaceMemberResponse
from ..model.delete_space_member_request import DeleteSpaceMemberRequest
from ..model.delete_space_member_response import DeleteSpaceMemberResponse
from ..model.list_space_member_request import ListSpaceMemberRequest
from ..model.list_space_member_response import ListSpaceMemberResponse


class SpaceMember(object):
    def __init__(self, config: Config) -> None:
        self.config: Config = config

    def create(self, request: CreateSpaceMemberRequest, option: Optional[RequestOption] = None) -> CreateSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        # 添加 content-type
        if request.body is not None:
            option.headers[CONTENT_TYPE] = f"{APPLICATION_JSON}; charset=utf-8"

        # 发起请求
        resp: RawResponse = Transport.execute(self.config, request, option)
        
        # 反序列化
        response: CreateSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), CreateSpaceMemberResponse)
        response.raw = resp

        return response
        

    async def acreate(self, request: CreateSpaceMemberRequest, option: Optional[RequestOption] = None) -> CreateSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        

        # 发起请求
        resp: RawResponse = await Transport.aexecute(self.config, request, option)
        
        # 反序列化
        response: CreateSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), CreateSpaceMemberResponse)
        response.raw = resp

        return response
        
    def delete(self, request: DeleteSpaceMemberRequest, option: Optional[RequestOption] = None) -> DeleteSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        # 添加 content-type
        if request.body is not None:
            option.headers[CONTENT_TYPE] = f"{APPLICATION_JSON}; charset=utf-8"

        # 发起请求
        resp: RawResponse = Transport.execute(self.config, request, option)
        
        # 反序列化
        response: DeleteSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), DeleteSpaceMemberResponse)
        response.raw = resp

        return response
        

    async def adelete(self, request: DeleteSpaceMemberRequest, option: Optional[RequestOption] = None) -> DeleteSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        

        # 发起请求
        resp: RawResponse = await Transport.aexecute(self.config, request, option)
        
        # 反序列化
        response: DeleteSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), DeleteSpaceMemberResponse)
        response.raw = resp

        return response
        
    def list(self, request: ListSpaceMemberRequest, option: Optional[RequestOption] = None) -> ListSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        # 添加 content-type
        if request.body is not None:
            option.headers[CONTENT_TYPE] = f"{APPLICATION_JSON}; charset=utf-8"

        # 发起请求
        resp: RawResponse = Transport.execute(self.config, request, option)
        
        # 反序列化
        response: ListSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), ListSpaceMemberResponse)
        response.raw = resp

        return response
        

    async def alist(self, request: ListSpaceMemberRequest, option: Optional[RequestOption] = None) -> ListSpaceMemberResponse:
        if option is None:
            option = RequestOption()

        # 鉴权、获取 token
        verify(self.config, request, option)

        

        # 发起请求
        resp: RawResponse = await Transport.aexecute(self.config, request, option)
        
        # 反序列化
        response: ListSpaceMemberResponse = JSON.unmarshal(str(resp.content, UTF_8), ListSpaceMemberResponse)
        response.raw = resp

        return response
        
    
