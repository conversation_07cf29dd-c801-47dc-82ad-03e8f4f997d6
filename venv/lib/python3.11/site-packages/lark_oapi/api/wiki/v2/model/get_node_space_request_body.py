# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class GetNodeSpaceRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "GetNodeSpaceRequestBodyBuilder":
        return GetNodeSpaceRequestBodyBuilder()


class GetNodeSpaceRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._get_node_space_request_body = GetNodeSpaceRequestBody()

    def build(self) -> "GetNodeSpaceRequestBody":
        return self._get_node_space_request_body
