# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .move_result import MoveResult


class TaskResult(object):
    _types = {
        "task_id": str,
        "move_result": List[MoveResult],
    }

    def __init__(self, d=None):
        self.task_id: Optional[str] = None
        self.move_result: Optional[List[MoveResult]] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "TaskResultBuilder":
        return TaskResultBuilder()


class TaskResultBuilder(object):
    def __init__(self) -> None:
        self._task_result = TaskResult()
    def task_id(self, task_id: str) -> "TaskResultBuilder":
        self._task_result.task_id = task_id
        return self
    def move_result(self, move_result: List[MoveResult]) -> "TaskResultBuilder":
        self._task_result.move_result = move_result
        return self
    
    def build(self) -> "TaskResult":
        return self._task_result