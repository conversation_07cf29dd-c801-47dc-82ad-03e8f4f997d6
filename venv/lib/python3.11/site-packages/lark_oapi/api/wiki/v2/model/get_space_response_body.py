# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .space import Space


class GetSpaceResponseBody(object):
    _types = {
        "space": Space,
    }

    def __init__(self, d=None):
        self.space: Optional[Space] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "GetSpaceResponseBodyBuilder":
        return GetSpaceResponseBodyBuilder()


class GetSpaceResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._get_space_response_body = GetSpaceResponseBody()
    def space(self, space: Space) -> "GetSpaceResponseBodyBuilder":
        self._get_space_response_body.space = space
        return self
    
    def build(self) -> "GetSpaceResponseBody":
        return self._get_space_response_body