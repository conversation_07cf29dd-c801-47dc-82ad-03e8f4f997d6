# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class ListSpaceMemberRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "ListSpaceMemberRequestBodyBuilder":
        return ListSpaceMemberRequestBodyBuilder()


class ListSpaceMemberRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._list_space_member_request_body = ListSpaceMemberRequestBody()

    def build(self) -> "ListSpaceMemberRequestBody":
        return self._list_space_member_request_body
