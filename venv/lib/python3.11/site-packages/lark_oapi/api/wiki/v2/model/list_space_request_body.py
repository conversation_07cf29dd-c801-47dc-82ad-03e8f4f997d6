# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class ListSpaceRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "ListSpaceRequestBodyBuilder":
        return ListSpaceRequestBodyBuilder()


class ListSpaceRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._list_space_request_body = ListSpaceRequestBody()

    def build(self) -> "ListSpaceRequestBody":
        return self._list_space_request_body
