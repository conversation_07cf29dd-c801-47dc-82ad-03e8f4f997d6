# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from lark_oapi.core.model import BaseResponse
from .get_node_space_response_body import GetNodeSpaceResponseBody


class GetNodeSpaceResponse(BaseResponse):
    _types = {
        "data": GetNodeSpaceResponseBody
    }

    def __init__(self, d=None):
        super().__init__(d)
        self.data: Optional[GetNodeSpaceResponseBody] = None
        init(self, d, self._types)
