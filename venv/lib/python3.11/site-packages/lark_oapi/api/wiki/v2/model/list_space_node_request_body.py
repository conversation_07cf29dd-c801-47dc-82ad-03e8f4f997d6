# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class ListSpaceNodeRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "ListSpaceNodeRequestBodyBuilder":
        return ListSpaceNodeRequestBodyBuilder()


class ListSpaceNodeRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._list_space_node_request_body = ListSpaceNodeRequestBody()

    def build(self) -> "ListSpaceNodeRequestBody":
        return self._list_space_node_request_body
