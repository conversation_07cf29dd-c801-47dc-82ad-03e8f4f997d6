# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .move_space_node_request_body import MoveSpaceNodeRequestBody


class MoveSpaceNodeRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.space_id: Optional[str] = None
        self.node_token: Optional[str] = None
        self.request_body: Optional[MoveSpaceNodeRequestBody] = None

    @staticmethod
    def builder() -> "MoveSpaceNodeRequestBuilder":
        return MoveSpaceNodeRequestBuilder()


class MoveSpaceNodeRequestBuilder(object):

    def __init__(self) -> None:
        move_space_node_request = MoveSpaceNodeRequest()
        move_space_node_request.http_method = HttpMethod.POST
        move_space_node_request.uri = "/open-apis/wiki/v2/spaces/:space_id/nodes/:node_token/move"
        move_space_node_request.token_types = {AccessTokenType.TENANT, AccessTokenType.USER}
        self._move_space_node_request: MoveSpaceNodeRequest = move_space_node_request
    
    def space_id(self, space_id: str) -> "MoveSpaceNodeRequestBuilder":
        self._move_space_node_request.space_id = space_id
        self._move_space_node_request.paths["space_id"] = str(space_id)
        return self
    
    def node_token(self, node_token: str) -> "MoveSpaceNodeRequestBuilder":
        self._move_space_node_request.node_token = node_token
        self._move_space_node_request.paths["node_token"] = str(node_token)
        return self
    
    def request_body(self, request_body: MoveSpaceNodeRequestBody) -> "MoveSpaceNodeRequestBuilder":
        self._move_space_node_request.request_body = request_body
        self._move_space_node_request.body = request_body
        return self

    def build(self) -> MoveSpaceNodeRequest:
        return self._move_space_node_request
