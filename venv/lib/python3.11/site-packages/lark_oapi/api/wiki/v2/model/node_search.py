# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class NodeSearch(object):
    _types = {
        "node_id": str,
        "space_id": str,
        "parent_id": str,
        "obj_type": int,
        "title": str,
        "url": str,
        "icon": str,
        "area_id": str,
        "sort_id": float,
        "domain": str,
        "obj_token": str,
    }

    def __init__(self, d=None):
        self.node_id: Optional[str] = None
        self.space_id: Optional[str] = None
        self.parent_id: Optional[str] = None
        self.obj_type: Optional[int] = None
        self.title: Optional[str] = None
        self.url: Optional[str] = None
        self.icon: Optional[str] = None
        self.area_id: Optional[str] = None
        self.sort_id: Optional[float] = None
        self.domain: Optional[str] = None
        self.obj_token: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "NodeSearchBuilder":
        return NodeSearchBuilder()


class NodeSearchBuilder(object):
    def __init__(self) -> None:
        self._node_search = NodeSearch()
    def node_id(self, node_id: str) -> "NodeSearchBuilder":
        self._node_search.node_id = node_id
        return self
    def space_id(self, space_id: str) -> "NodeSearchBuilder":
        self._node_search.space_id = space_id
        return self
    def parent_id(self, parent_id: str) -> "NodeSearchBuilder":
        self._node_search.parent_id = parent_id
        return self
    def obj_type(self, obj_type: int) -> "NodeSearchBuilder":
        self._node_search.obj_type = obj_type
        return self
    def title(self, title: str) -> "NodeSearchBuilder":
        self._node_search.title = title
        return self
    def url(self, url: str) -> "NodeSearchBuilder":
        self._node_search.url = url
        return self
    def icon(self, icon: str) -> "NodeSearchBuilder":
        self._node_search.icon = icon
        return self
    def area_id(self, area_id: str) -> "NodeSearchBuilder":
        self._node_search.area_id = area_id
        return self
    def sort_id(self, sort_id: float) -> "NodeSearchBuilder":
        self._node_search.sort_id = sort_id
        return self
    def domain(self, domain: str) -> "NodeSearchBuilder":
        self._node_search.domain = domain
        return self
    def obj_token(self, obj_token: str) -> "NodeSearchBuilder":
        self._node_search.obj_token = obj_token
        return self
    
    def build(self) -> "NodeSearch":
        return self._node_search