# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class UpdateTitleSpaceNodeResponseBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "UpdateTitleSpaceNodeResponseBodyBuilder":
        return UpdateTitleSpaceNodeResponseBodyBuilder()


class UpdateTitleSpaceNodeResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._update_title_space_node_response_body = UpdateTitleSpaceNodeResponseBody()

    def build(self) -> "UpdateTitleSpaceNodeResponseBody":
        return self._update_title_space_node_response_body
