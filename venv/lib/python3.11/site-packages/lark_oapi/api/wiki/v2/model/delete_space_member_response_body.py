# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .member import Member


class DeleteSpaceMemberResponseBody(object):
    _types = {
        "member": Member,
    }

    def __init__(self, d=None):
        self.member: Optional[Member] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "DeleteSpaceMemberResponseBodyBuilder":
        return DeleteSpaceMemberResponseBodyBuilder()


class DeleteSpaceMemberResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._delete_space_member_response_body = DeleteSpaceMemberResponseBody()
    def member(self, member: Member) -> "DeleteSpaceMemberResponseBodyBuilder":
        self._delete_space_member_response_body.member = member
        return self
    
    def build(self) -> "DeleteSpaceMemberResponseBody":
        return self._delete_space_member_response_body