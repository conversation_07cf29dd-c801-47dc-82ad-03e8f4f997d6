# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .node import Node


class MoveSpaceNodeResponseBody(object):
    _types = {
        "node": Node,
    }

    def __init__(self, d=None):
        self.node: Optional[Node] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "MoveSpaceNodeResponseBodyBuilder":
        return MoveSpaceNodeResponseBodyBuilder()


class MoveSpaceNodeResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._move_space_node_response_body = MoveSpaceNodeResponseBody()
    def node(self, node: Node) -> "MoveSpaceNodeResponseBodyBuilder":
        self._move_space_node_response_body.node = node
        return self
    
    def build(self) -> "MoveSpaceNodeResponseBody":
        return self._move_space_node_response_body