# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .member import Member


class DeleteSpaceMemberRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.space_id: Optional[str] = None
        self.member_id: Optional[str] = None
        self.request_body: Optional[Member] = None

    @staticmethod
    def builder() -> "DeleteSpaceMemberRequestBuilder":
        return DeleteSpaceMemberRequestBuilder()


class DeleteSpaceMemberRequestBuilder(object):

    def __init__(self) -> None:
        delete_space_member_request = DeleteSpaceMemberRequest()
        delete_space_member_request.http_method = HttpMethod.DELETE
        delete_space_member_request.uri = "/open-apis/wiki/v2/spaces/:space_id/members/:member_id"
        delete_space_member_request.token_types = {AccessTokenType.TENANT, AccessTokenType.USER}
        self._delete_space_member_request: DeleteSpaceMemberRequest = delete_space_member_request
    
    def space_id(self, space_id: str) -> "DeleteSpaceMemberRequestBuilder":
        self._delete_space_member_request.space_id = space_id
        self._delete_space_member_request.paths["space_id"] = str(space_id)
        return self
    
    def member_id(self, member_id: str) -> "DeleteSpaceMemberRequestBuilder":
        self._delete_space_member_request.member_id = member_id
        self._delete_space_member_request.paths["member_id"] = str(member_id)
        return self
    
    def request_body(self, request_body: Member) -> "DeleteSpaceMemberRequestBuilder":
        self._delete_space_member_request.request_body = request_body
        self._delete_space_member_request.body = request_body
        return self

    def build(self) -> DeleteSpaceMemberRequest:
        return self._delete_space_member_request
