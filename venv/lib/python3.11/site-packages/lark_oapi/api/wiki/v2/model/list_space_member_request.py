# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType


class ListSpaceMemberRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.page_size: Optional[int] = None
        self.page_token: Optional[str] = None
        self.space_id: Optional[str] = None

    @staticmethod
    def builder() -> "ListSpaceMemberRequestBuilder":
        return ListSpaceMemberRequestBuilder()


class ListSpaceMemberRequestBuilder(object):

    def __init__(self) -> None:
        list_space_member_request = ListSpaceMemberRequest()
        list_space_member_request.http_method = HttpMethod.GET
        list_space_member_request.uri = "/open-apis/wiki/v2/spaces/:space_id/members"
        list_space_member_request.token_types = {AccessTokenType.TENANT, AccessTokenType.USER}
        self._list_space_member_request: ListSpaceMemberRequest = list_space_member_request
    
    def page_size(self, page_size: int) -> "ListSpaceMemberRequestBuilder":
        self._list_space_member_request.page_size = page_size
        self._list_space_member_request.add_query("page_size", page_size)
        return self
    
    def page_token(self, page_token: str) -> "ListSpaceMemberRequestBuilder":
        self._list_space_member_request.page_token = page_token
        self._list_space_member_request.add_query("page_token", page_token)
        return self
    
    def space_id(self, space_id: str) -> "ListSpaceMemberRequestBuilder":
        self._list_space_member_request.space_id = space_id
        self._list_space_member_request.paths["space_id"] = str(space_id)
        return self
    

    def build(self) -> ListSpaceMemberRequest:
        return self._list_space_member_request
