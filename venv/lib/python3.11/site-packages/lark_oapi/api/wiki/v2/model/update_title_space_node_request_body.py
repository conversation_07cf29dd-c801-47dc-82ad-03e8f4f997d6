# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class UpdateTitleSpaceNodeRequestBody(object):
    _types = {
        "title": str,
    }

    def __init__(self, d=None):
        self.title: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "UpdateTitleSpaceNodeRequestBodyBuilder":
        return UpdateTitleSpaceNodeRequestBodyBuilder()


class UpdateTitleSpaceNodeRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._update_title_space_node_request_body = UpdateTitleSpaceNodeRequestBody()
    def title(self, title: str) -> "UpdateTitleSpaceNodeRequestBodyBuilder":
        self._update_title_space_node_request_body.title = title
        return self
    
    def build(self) -> "UpdateTitleSpaceNodeRequestBody":
        return self._update_title_space_node_request_body