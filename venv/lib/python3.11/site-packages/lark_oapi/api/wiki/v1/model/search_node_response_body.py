# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from .node import Node


class SearchNodeResponseBody(object):
    _types = {
        "items": List[Node],
        "page_token": str,
        "has_more": bool,
    }

    def __init__(self, d=None):
        self.items: Optional[List[Node]] = None
        self.page_token: Optional[str] = None
        self.has_more: Optional[bool] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchNodeResponseBodyBuilder":
        return SearchNodeResponseBodyBuilder()


class SearchNodeResponseBodyBuilder(object):
    def __init__(self) -> None:
        self._search_node_response_body = SearchNodeResponseBody()
    def items(self, items: List[Node]) -> "SearchNodeResponseBodyBuilder":
        self._search_node_response_body.items = items
        return self
    def page_token(self, page_token: str) -> "SearchNodeResponseBodyBuilder":
        self._search_node_response_body.page_token = page_token
        return self
    def has_more(self, has_more: bool) -> "SearchNodeResponseBodyBuilder":
        self._search_node_response_body.has_more = has_more
        return self
    
    def build(self) -> "SearchNodeResponseBody":
        return self._search_node_response_body