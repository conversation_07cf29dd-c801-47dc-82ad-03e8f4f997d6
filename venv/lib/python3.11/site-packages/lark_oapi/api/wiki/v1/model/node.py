# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class Node(object):
    _types = {
        "node_id": str,
        "space_id": str,
        "parent_id": str,
        "obj_type": int,
        "title": str,
        "url": str,
        "icon": str,
        "area_id": str,
        "sort_id": float,
        "domain": str,
        "obj_token": str,
        "create_time": str,
        "update_time": str,
        "delete_time": str,
        "child_num": int,
        "version": int,
    }

    def __init__(self, d=None):
        self.node_id: Optional[str] = None
        self.space_id: Optional[str] = None
        self.parent_id: Optional[str] = None
        self.obj_type: Optional[int] = None
        self.title: Optional[str] = None
        self.url: Optional[str] = None
        self.icon: Optional[str] = None
        self.area_id: Optional[str] = None
        self.sort_id: Optional[float] = None
        self.domain: Optional[str] = None
        self.obj_token: Optional[str] = None
        self.create_time: Optional[str] = None
        self.update_time: Optional[str] = None
        self.delete_time: Optional[str] = None
        self.child_num: Optional[int] = None
        self.version: Optional[int] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "NodeBuilder":
        return NodeBuilder()


class NodeBuilder(object):
    def __init__(self) -> None:
        self._node = Node()
    def node_id(self, node_id: str) -> "NodeBuilder":
        self._node.node_id = node_id
        return self
    def space_id(self, space_id: str) -> "NodeBuilder":
        self._node.space_id = space_id
        return self
    def parent_id(self, parent_id: str) -> "NodeBuilder":
        self._node.parent_id = parent_id
        return self
    def obj_type(self, obj_type: int) -> "NodeBuilder":
        self._node.obj_type = obj_type
        return self
    def title(self, title: str) -> "NodeBuilder":
        self._node.title = title
        return self
    def url(self, url: str) -> "NodeBuilder":
        self._node.url = url
        return self
    def icon(self, icon: str) -> "NodeBuilder":
        self._node.icon = icon
        return self
    def area_id(self, area_id: str) -> "NodeBuilder":
        self._node.area_id = area_id
        return self
    def sort_id(self, sort_id: float) -> "NodeBuilder":
        self._node.sort_id = sort_id
        return self
    def domain(self, domain: str) -> "NodeBuilder":
        self._node.domain = domain
        return self
    def obj_token(self, obj_token: str) -> "NodeBuilder":
        self._node.obj_token = obj_token
        return self
    def create_time(self, create_time: str) -> "NodeBuilder":
        self._node.create_time = create_time
        return self
    def update_time(self, update_time: str) -> "NodeBuilder":
        self._node.update_time = update_time
        return self
    def delete_time(self, delete_time: str) -> "NodeBuilder":
        self._node.delete_time = delete_time
        return self
    def child_num(self, child_num: int) -> "NodeBuilder":
        self._node.child_num = child_num
        return self
    def version(self, version: int) -> "NodeBuilder":
        self._node.version = version
        return self
    
    def build(self) -> "Node":
        return self._node