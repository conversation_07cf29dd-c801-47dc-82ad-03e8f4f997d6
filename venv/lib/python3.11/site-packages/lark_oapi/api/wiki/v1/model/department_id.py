# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class DepartmentId(object):
    _types = {
        "department_id": str,
        "open_department_id": str,
    }

    def __init__(self, d=None):
        self.department_id: Optional[str] = None
        self.open_department_id: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "DepartmentIdBuilder":
        return DepartmentIdBuilder()


class DepartmentIdBuilder(object):
    def __init__(self) -> None:
        self._department_id = DepartmentId()
    def department_id(self, department_id: str) -> "DepartmentIdBuilder":
        self._department_id.department_id = department_id
        return self
    def open_department_id(self, open_department_id: str) -> "DepartmentIdBuilder":
        self._department_id.open_department_id = open_department_id
        return self
    
    def build(self) -> "DepartmentId":
        return self._department_id