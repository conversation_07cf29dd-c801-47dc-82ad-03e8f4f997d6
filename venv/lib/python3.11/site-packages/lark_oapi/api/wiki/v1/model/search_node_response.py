# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from lark_oapi.core.model import BaseResponse
from .search_node_response_body import SearchNodeResponseBody


class SearchNodeResponse(BaseResponse):
    _types = {
        "data": SearchNodeResponseBody
    }

    def __init__(self, d=None):
        super().__init__(d)
        self.data: Optional[SearchNodeResponseBody] = None
        init(self, d, self._types)
