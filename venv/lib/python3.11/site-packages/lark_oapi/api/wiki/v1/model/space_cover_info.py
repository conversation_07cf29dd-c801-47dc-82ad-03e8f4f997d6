# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SpaceCoverInfo(object):
    _types = {
        "origin": str,
        "thumbnail": str,
        "name": str,
        "is_graph_dark": bool,
        "color": str,
    }

    def __init__(self, d=None):
        self.origin: Optional[str] = None
        self.thumbnail: Optional[str] = None
        self.name: Optional[str] = None
        self.is_graph_dark: Optional[bool] = None
        self.color: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SpaceCoverInfoBuilder":
        return SpaceCoverInfoBuilder()


class SpaceCoverInfoBuilder(object):
    def __init__(self) -> None:
        self._space_cover_info = SpaceCoverInfo()
    def origin(self, origin: str) -> "SpaceCoverInfoBuilder":
        self._space_cover_info.origin = origin
        return self
    def thumbnail(self, thumbnail: str) -> "SpaceCoverInfoBuilder":
        self._space_cover_info.thumbnail = thumbnail
        return self
    def name(self, name: str) -> "SpaceCoverInfoBuilder":
        self._space_cover_info.name = name
        return self
    def is_graph_dark(self, is_graph_dark: bool) -> "SpaceCoverInfoBuilder":
        self._space_cover_info.is_graph_dark = is_graph_dark
        return self
    def color(self, color: str) -> "SpaceCoverInfoBuilder":
        self._space_cover_info.color = color
        return self
    
    def build(self) -> "SpaceCoverInfo":
        return self._space_cover_info