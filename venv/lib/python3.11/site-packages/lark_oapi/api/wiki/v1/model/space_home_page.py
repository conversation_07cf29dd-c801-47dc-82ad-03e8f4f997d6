# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SpaceHomePage(object):
    _types = {
        "node_id": str,
        "obj_token": str,
        "obj_type": int,
        "url": str,
    }

    def __init__(self, d=None):
        self.node_id: Optional[str] = None
        self.obj_token: Optional[str] = None
        self.obj_type: Optional[int] = None
        self.url: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SpaceHomePageBuilder":
        return SpaceHomePageBuilder()


class SpaceHomePageBuilder(object):
    def __init__(self) -> None:
        self._space_home_page = SpaceHomePage()
    def node_id(self, node_id: str) -> "SpaceHomePageBuilder":
        self._space_home_page.node_id = node_id
        return self
    def obj_token(self, obj_token: str) -> "SpaceHomePageBuilder":
        self._space_home_page.obj_token = obj_token
        return self
    def obj_type(self, obj_type: int) -> "SpaceHomePageBuilder":
        self._space_home_page.obj_type = obj_type
        return self
    def url(self, url: str) -> "SpaceHomePageBuilder":
        self._space_home_page.url = url
        return self
    
    def build(self) -> "SpaceHomePage":
        return self._space_home_page