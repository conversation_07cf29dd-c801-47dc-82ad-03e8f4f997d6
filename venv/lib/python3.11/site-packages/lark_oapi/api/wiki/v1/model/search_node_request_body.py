# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class SearchNodeRequestBody(object):
    _types = {
        "query": str,
        "space_id": str,
        "node_id": str,
    }

    def __init__(self, d=None):
        self.query: Optional[str] = None
        self.space_id: Optional[str] = None
        self.node_id: Optional[str] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "SearchNodeRequestBodyBuilder":
        return SearchNodeRequestBodyBuilder()


class SearchNodeRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._search_node_request_body = SearchNodeRequestBody()
    def query(self, query: str) -> "SearchNodeRequestBodyBuilder":
        self._search_node_request_body.query = query
        return self
    def space_id(self, space_id: str) -> "SearchNodeRequestBodyBuilder":
        self._search_node_request_body.space_id = space_id
        return self
    def node_id(self, node_id: str) -> "SearchNodeRequestBodyBuilder":
        self._search_node_request_body.node_id = node_id
        return self
    
    def build(self) -> "SearchNodeRequestBody":
        return self._search_node_request_body