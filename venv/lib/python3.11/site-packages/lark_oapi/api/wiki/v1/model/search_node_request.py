# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.model import BaseRequest
from lark_oapi.core.enum import HttpMethod, AccessTokenType
from .search_node_request_body import SearchNodeRequestBody


class SearchNodeRequest(BaseRequest):
    def __init__(self) -> None:
        super().__init__()
        self.page_token: Optional[str] = None
        self.page_size: Optional[int] = None
        self.request_body: Optional[SearchNodeRequestBody] = None

    @staticmethod
    def builder() -> "SearchNodeRequestBuilder":
        return SearchNodeRequestBuilder()


class SearchNodeRequestBuilder(object):

    def __init__(self) -> None:
        search_node_request = SearchNodeRequest()
        search_node_request.http_method = HttpMethod.POST
        search_node_request.uri = "/open-apis/wiki/v1/nodes/search"
        search_node_request.token_types = {AccessTokenType.USER}
        self._search_node_request: SearchNodeRequest = search_node_request
    
    def page_token(self, page_token: str) -> "SearchNodeRequestBuilder":
        self._search_node_request.page_token = page_token
        self._search_node_request.add_query("page_token", page_token)
        return self
    
    def page_size(self, page_size: int) -> "SearchNodeRequestBuilder":
        self._search_node_request.page_size = page_size
        self._search_node_request.add_query("page_size", page_size)
        return self
    
    def request_body(self, request_body: SearchNodeRequestBody) -> "SearchNodeRequestBuilder":
        self._search_node_request.request_body = request_body
        self._search_node_request.body = request_body
        return self

    def build(self) -> SearchNodeRequest:
        return self._search_node_request
