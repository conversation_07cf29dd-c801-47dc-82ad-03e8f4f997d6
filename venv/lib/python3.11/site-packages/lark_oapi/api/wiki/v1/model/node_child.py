# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class NodeChild(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "NodeChildBuilder":
        return NodeChildBuilder()


class NodeChildBuilder(object):
    def __init__(self) -> None:
        self._node_child = NodeChild()
    
    def build(self) -> "NodeChild":
        return self._node_child