# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class GetVerificationRequestBody(object):
    _types = {
    }

    def __init__(self, d=None):
        init(self, d, self._types)

    @staticmethod
    def builder() -> "GetVerificationRequestBodyBuilder":
        return GetVerificationRequestBodyBuilder()


class GetVerificationRequestBodyBuilder(object):
    def __init__(self) -> None:
        self._get_verification_request_body = GetVerificationRequestBody()

    def build(self) -> "GetVerificationRequestBody":
        return self._get_verification_request_body
