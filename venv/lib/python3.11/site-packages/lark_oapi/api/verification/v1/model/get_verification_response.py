# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init
from lark_oapi.core.model import BaseResponse
from .get_verification_response_body import GetVerificationResponseBody


class GetVerificationResponse(BaseResponse):
    _types = {
        "data": GetVerificationResponseBody
    }

    def __init__(self, d=None):
        super().__init__(d)
        self.data: Optional[GetVerificationResponseBody] = None
        init(self, d, self._types)
