# Code generated by Lark OpenAPI.

from typing import Any, Optional, Union, Dict, List, Set, IO, Callable, Type
from lark_oapi.core.construct import init


class Verification(object):
    _types = {
        "name": str,
        "has_verification": bool,
    }

    def __init__(self, d=None):
        self.name: Optional[str] = None
        self.has_verification: Optional[bool] = None
        init(self, d, self._types)

    @staticmethod
    def builder() -> "VerificationBuilder":
        return VerificationBuilder()


class VerificationBuilder(object):
    def __init__(self) -> None:
        self._verification = Verification()
    def name(self, name: str) -> "VerificationBuilder":
        self._verification.name = name
        return self
    def has_verification(self, has_verification: bool) -> "VerificationBuilder":
        self._verification.has_verification = has_verification
        return self
    
    def build(self) -> "Verification":
        return self._verification