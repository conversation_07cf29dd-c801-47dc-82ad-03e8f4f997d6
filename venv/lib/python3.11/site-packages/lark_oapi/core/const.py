# Info
PROJECT = "oapi-sdk-python"
VERSION = "1.4.19"

# Domain
FEISHU_DOMAIN = "https://open.feishu.cn"
LARK_DOMAIN = "https://open.larksuite.com"

# Header
USER_AGENT = "User-Agent"
AUTHORIZATION = "Authorization"
X_TT_LOGID = "X-Tt-Logid"
X_REQUEST_ID = "X-Request-Id"
CONTENT_TYPE = "Content-Type"
Content_Disposition = "Content-Disposition"
LARK_REQUEST_TIMESTAMP = "X-Lark-Request-Timestamp"
LARK_REQUEST_NONCE = "X-Lark-Request-Nonce"
LARK_REQUEST_SIGNATURE = "X-Lark-Signature"

# Content-Type
APPLICATION_JSON = "application/json"

# Event
URL_VERIFICATION = "url_verification"

UTF_8 = "UTF-8"
