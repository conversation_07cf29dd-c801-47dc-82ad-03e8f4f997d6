# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/descriptor.proto
"""Generated protocol buffer code."""
from lark_oapi.ws.pb.google.protobuf.internal import builder as _builder
from lark_oapi.ws.pb.google.protobuf import descriptor as _descriptor
from lark_oapi.ws.pb.google.protobuf import descriptor_pool as _descriptor_pool
from lark_oapi.ws.pb.google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR = _descriptor.FileDescriptor(
        name='google/protobuf/descriptor.proto',
        package='google.protobuf',
        syntax='proto2',
        serialized_options=None,
        create_key=_descriptor._internal_create_key,
        serialized_pb=b'\n google/protobuf/descriptor.proto\x12\x0fgoogle.protobuf\"G\n\x11\x46ileDescriptorSet\x12\x32\n\x04\x66ile\x18\x01 \x03(\x0b\x32$.google.protobuf.FileDescriptorProto\"\xdb\x03\n\x13\x46ileDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07package\x18\x02 \x01(\t\x12\x12\n\ndependency\x18\x03 \x03(\t\x12\x19\n\x11public_dependency\x18\n \x03(\x05\x12\x17\n\x0fweak_dependency\x18\x0b \x03(\x05\x12\x36\n\x0cmessage_type\x18\x04 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x05 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12\x38\n\x07service\x18\x06 \x03(\x0b\x32\'.google.protobuf.ServiceDescriptorProto\x12\x38\n\textension\x18\x07 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12-\n\x07options\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.FileOptions\x12\x39\n\x10source_code_info\x18\t \x01(\x0b\x32\x1f.google.protobuf.SourceCodeInfo\x12\x0e\n\x06syntax\x18\x0c \x01(\t\"\xa9\x05\n\x0f\x44\x65scriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x34\n\x05\x66ield\x18\x02 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x38\n\textension\x18\x06 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x35\n\x0bnested_type\x18\x03 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x04 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12H\n\x0f\x65xtension_range\x18\x05 \x03(\x0b\x32/.google.protobuf.DescriptorProto.ExtensionRange\x12\x39\n\noneof_decl\x18\x08 \x03(\x0b\x32%.google.protobuf.OneofDescriptorProto\x12\x30\n\x07options\x18\x07 \x01(\x0b\x32\x1f.google.protobuf.MessageOptions\x12\x46\n\x0ereserved_range\x18\t \x03(\x0b\x32..google.protobuf.DescriptorProto.ReservedRange\x12\x15\n\rreserved_name\x18\n \x03(\t\x1a\x65\n\x0e\x45xtensionRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\x12\x37\n\x07options\x18\x03 \x01(\x0b\x32&.google.protobuf.ExtensionRangeOptions\x1a+\n\rReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"g\n\x15\x45xtensionRangeOptions\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xd5\x05\n\x14\x46ieldDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x03 \x01(\x05\x12:\n\x05label\x18\x04 \x01(\x0e\x32+.google.protobuf.FieldDescriptorProto.Label\x12\x38\n\x04type\x18\x05 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12\x11\n\ttype_name\x18\x06 \x01(\t\x12\x10\n\x08\x65xtendee\x18\x02 \x01(\t\x12\x15\n\rdefault_value\x18\x07 \x01(\t\x12\x13\n\x0boneof_index\x18\t \x01(\x05\x12\x11\n\tjson_name\x18\n \x01(\t\x12.\n\x07options\x18\x08 \x01(\x0b\x32\x1d.google.protobuf.FieldOptions\x12\x17\n\x0fproto3_optional\x18\x11 \x01(\x08\"\xb6\x02\n\x04Type\x12\x0f\n\x0bTYPE_DOUBLE\x10\x01\x12\x0e\n\nTYPE_FLOAT\x10\x02\x12\x0e\n\nTYPE_INT64\x10\x03\x12\x0f\n\x0bTYPE_UINT64\x10\x04\x12\x0e\n\nTYPE_INT32\x10\x05\x12\x10\n\x0cTYPE_FIXED64\x10\x06\x12\x10\n\x0cTYPE_FIXED32\x10\x07\x12\r\n\tTYPE_BOOL\x10\x08\x12\x0f\n\x0bTYPE_STRING\x10\t\x12\x0e\n\nTYPE_GROUP\x10\n\x12\x10\n\x0cTYPE_MESSAGE\x10\x0b\x12\x0e\n\nTYPE_BYTES\x10\x0c\x12\x0f\n\x0bTYPE_UINT32\x10\r\x12\r\n\tTYPE_ENUM\x10\x0e\x12\x11\n\rTYPE_SFIXED32\x10\x0f\x12\x11\n\rTYPE_SFIXED64\x10\x10\x12\x0f\n\x0bTYPE_SINT32\x10\x11\x12\x0f\n\x0bTYPE_SINT64\x10\x12\"C\n\x05Label\x12\x12\n\x0eLABEL_OPTIONAL\x10\x01\x12\x12\n\x0eLABEL_REQUIRED\x10\x02\x12\x12\n\x0eLABEL_REPEATED\x10\x03\"T\n\x14OneofDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x07options\x18\x02 \x01(\x0b\x32\x1d.google.protobuf.OneofOptions\"\xa4\x02\n\x13\x45numDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x38\n\x05value\x18\x02 \x03(\x0b\x32).google.protobuf.EnumValueDescriptorProto\x12-\n\x07options\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.EnumOptions\x12N\n\x0ereserved_range\x18\x04 \x03(\x0b\x32\x36.google.protobuf.EnumDescriptorProto.EnumReservedRange\x12\x15\n\rreserved_name\x18\x05 \x03(\t\x1a/\n\x11\x45numReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"l\n\x18\x45numValueDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x02 \x01(\x05\x12\x32\n\x07options\x18\x03 \x01(\x0b\x32!.google.protobuf.EnumValueOptions\"\x90\x01\n\x16ServiceDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x36\n\x06method\x18\x02 \x03(\x0b\x32&.google.protobuf.MethodDescriptorProto\x12\x30\n\x07options\x18\x03 \x01(\x0b\x32\x1f.google.protobuf.ServiceOptions\"\xc1\x01\n\x15MethodDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ninput_type\x18\x02 \x01(\t\x12\x13\n\x0boutput_type\x18\x03 \x01(\t\x12/\n\x07options\x18\x04 \x01(\x0b\x32\x1e.google.protobuf.MethodOptions\x12\x1f\n\x10\x63lient_streaming\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10server_streaming\x18\x06 \x01(\x08:\x05\x66\x61lse\"\xa5\x06\n\x0b\x46ileOptions\x12\x14\n\x0cjava_package\x18\x01 \x01(\t\x12\x1c\n\x14java_outer_classname\x18\x08 \x01(\t\x12\"\n\x13java_multiple_files\x18\n \x01(\x08:\x05\x66\x61lse\x12)\n\x1djava_generate_equals_and_hash\x18\x14 \x01(\x08\x42\x02\x18\x01\x12%\n\x16java_string_check_utf8\x18\x1b \x01(\x08:\x05\x66\x61lse\x12\x46\n\x0coptimize_for\x18\t \x01(\x0e\x32).google.protobuf.FileOptions.OptimizeMode:\x05SPEED\x12\x12\n\ngo_package\x18\x0b \x01(\t\x12\"\n\x13\x63\x63_generic_services\x18\x10 \x01(\x08:\x05\x66\x61lse\x12$\n\x15java_generic_services\x18\x11 \x01(\x08:\x05\x66\x61lse\x12\"\n\x13py_generic_services\x18\x12 \x01(\x08:\x05\x66\x61lse\x12#\n\x14php_generic_services\x18* \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x17 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x10\x63\x63_enable_arenas\x18\x1f \x01(\x08:\x04true\x12\x19\n\x11objc_class_prefix\x18$ \x01(\t\x12\x18\n\x10\x63sharp_namespace\x18% \x01(\t\x12\x14\n\x0cswift_prefix\x18\' \x01(\t\x12\x18\n\x10php_class_prefix\x18( \x01(\t\x12\x15\n\rphp_namespace\x18) \x01(\t\x12\x1e\n\x16php_metadata_namespace\x18, \x01(\t\x12\x14\n\x0cruby_package\x18- \x01(\t\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\":\n\x0cOptimizeMode\x12\t\n\x05SPEED\x10\x01\x12\r\n\tCODE_SIZE\x10\x02\x12\x10\n\x0cLITE_RUNTIME\x10\x03*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08&\x10\'\"\x84\x02\n\x0eMessageOptions\x12&\n\x17message_set_wire_format\x18\x01 \x01(\x08:\x05\x66\x61lse\x12.\n\x1fno_standard_descriptor_accessor\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x11\n\tmap_entry\x18\x07 \x01(\x08\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n\"\xbe\x03\n\x0c\x46ieldOptions\x12:\n\x05\x63type\x18\x01 \x01(\x0e\x32#.google.protobuf.FieldOptions.CType:\x06STRING\x12\x0e\n\x06packed\x18\x02 \x01(\x08\x12?\n\x06jstype\x18\x06 \x01(\x0e\x32$.google.protobuf.FieldOptions.JSType:\tJS_NORMAL\x12\x13\n\x04lazy\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0funverified_lazy\x18\x0f \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x13\n\x04weak\x18\n \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\"/\n\x05\x43Type\x12\n\n\x06STRING\x10\x00\x12\x08\n\x04\x43ORD\x10\x01\x12\x10\n\x0cSTRING_PIECE\x10\x02\"5\n\x06JSType\x12\r\n\tJS_NORMAL\x10\x00\x12\r\n\tJS_STRING\x10\x01\x12\r\n\tJS_NUMBER\x10\x02*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05\"^\n\x0cOneofOptions\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x93\x01\n\x0b\x45numOptions\x12\x13\n\x0b\x61llow_alias\x18\x02 \x01(\x08\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x05\x10\x06\"}\n\x10\x45numValueOptions\x12\x19\n\ndeprecated\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"{\n\x0eServiceOptions\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xad\x02\n\rMethodOptions\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12_\n\x11idempotency_level\x18\" \x01(\x0e\x32/.google.protobuf.MethodOptions.IdempotencyLevel:\x13IDEMPOTENCY_UNKNOWN\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\"P\n\x10IdempotencyLevel\x12\x17\n\x13IDEMPOTENCY_UNKNOWN\x10\x00\x12\x13\n\x0fNO_SIDE_EFFECTS\x10\x01\x12\x0e\n\nIDEMPOTENT\x10\x02*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x9e\x02\n\x13UninterpretedOption\x12;\n\x04name\x18\x02 \x03(\x0b\x32-.google.protobuf.UninterpretedOption.NamePart\x12\x18\n\x10identifier_value\x18\x03 \x01(\t\x12\x1a\n\x12positive_int_value\x18\x04 \x01(\x04\x12\x1a\n\x12negative_int_value\x18\x05 \x01(\x03\x12\x14\n\x0c\x64ouble_value\x18\x06 \x01(\x01\x12\x14\n\x0cstring_value\x18\x07 \x01(\x0c\x12\x17\n\x0f\x61ggregate_value\x18\x08 \x01(\t\x1a\x33\n\x08NamePart\x12\x11\n\tname_part\x18\x01 \x02(\t\x12\x14\n\x0cis_extension\x18\x02 \x02(\x08\"\xd5\x01\n\x0eSourceCodeInfo\x12:\n\x08location\x18\x01 \x03(\x0b\x32(.google.protobuf.SourceCodeInfo.Location\x1a\x86\x01\n\x08Location\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x10\n\x04span\x18\x02 \x03(\x05\x42\x02\x10\x01\x12\x18\n\x10leading_comments\x18\x03 \x01(\t\x12\x19\n\x11trailing_comments\x18\x04 \x01(\t\x12!\n\x19leading_detached_comments\x18\x06 \x03(\t\"\xa7\x01\n\x11GeneratedCodeInfo\x12\x41\n\nannotation\x18\x01 \x03(\x0b\x32-.google.protobuf.GeneratedCodeInfo.Annotation\x1aO\n\nAnnotation\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x13\n\x0bsource_file\x18\x02 \x01(\t\x12\r\n\x05\x62\x65gin\x18\x03 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x04 \x01(\x05\x42~\n\x13\x63om.google.protobufB\x10\x44\x65scriptorProtosH\x01Z-google.golang.org/protobuf/types/descriptorpb\xf8\x01\x01\xa2\x02\x03GPB\xaa\x02\x1aGoogle.Protobuf.Reflection'
    )
else:
    DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
        b'\n google/protobuf/descriptor.proto\x12\x0fgoogle.protobuf\"G\n\x11\x46ileDescriptorSet\x12\x32\n\x04\x66ile\x18\x01 \x03(\x0b\x32$.google.protobuf.FileDescriptorProto\"\xdb\x03\n\x13\x46ileDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07package\x18\x02 \x01(\t\x12\x12\n\ndependency\x18\x03 \x03(\t\x12\x19\n\x11public_dependency\x18\n \x03(\x05\x12\x17\n\x0fweak_dependency\x18\x0b \x03(\x05\x12\x36\n\x0cmessage_type\x18\x04 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x05 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12\x38\n\x07service\x18\x06 \x03(\x0b\x32\'.google.protobuf.ServiceDescriptorProto\x12\x38\n\textension\x18\x07 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12-\n\x07options\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.FileOptions\x12\x39\n\x10source_code_info\x18\t \x01(\x0b\x32\x1f.google.protobuf.SourceCodeInfo\x12\x0e\n\x06syntax\x18\x0c \x01(\t\"\xa9\x05\n\x0f\x44\x65scriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x34\n\x05\x66ield\x18\x02 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x38\n\textension\x18\x06 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x35\n\x0bnested_type\x18\x03 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x04 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12H\n\x0f\x65xtension_range\x18\x05 \x03(\x0b\x32/.google.protobuf.DescriptorProto.ExtensionRange\x12\x39\n\noneof_decl\x18\x08 \x03(\x0b\x32%.google.protobuf.OneofDescriptorProto\x12\x30\n\x07options\x18\x07 \x01(\x0b\x32\x1f.google.protobuf.MessageOptions\x12\x46\n\x0ereserved_range\x18\t \x03(\x0b\x32..google.protobuf.DescriptorProto.ReservedRange\x12\x15\n\rreserved_name\x18\n \x03(\t\x1a\x65\n\x0e\x45xtensionRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\x12\x37\n\x07options\x18\x03 \x01(\x0b\x32&.google.protobuf.ExtensionRangeOptions\x1a+\n\rReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"g\n\x15\x45xtensionRangeOptions\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xd5\x05\n\x14\x46ieldDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x03 \x01(\x05\x12:\n\x05label\x18\x04 \x01(\x0e\x32+.google.protobuf.FieldDescriptorProto.Label\x12\x38\n\x04type\x18\x05 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12\x11\n\ttype_name\x18\x06 \x01(\t\x12\x10\n\x08\x65xtendee\x18\x02 \x01(\t\x12\x15\n\rdefault_value\x18\x07 \x01(\t\x12\x13\n\x0boneof_index\x18\t \x01(\x05\x12\x11\n\tjson_name\x18\n \x01(\t\x12.\n\x07options\x18\x08 \x01(\x0b\x32\x1d.google.protobuf.FieldOptions\x12\x17\n\x0fproto3_optional\x18\x11 \x01(\x08\"\xb6\x02\n\x04Type\x12\x0f\n\x0bTYPE_DOUBLE\x10\x01\x12\x0e\n\nTYPE_FLOAT\x10\x02\x12\x0e\n\nTYPE_INT64\x10\x03\x12\x0f\n\x0bTYPE_UINT64\x10\x04\x12\x0e\n\nTYPE_INT32\x10\x05\x12\x10\n\x0cTYPE_FIXED64\x10\x06\x12\x10\n\x0cTYPE_FIXED32\x10\x07\x12\r\n\tTYPE_BOOL\x10\x08\x12\x0f\n\x0bTYPE_STRING\x10\t\x12\x0e\n\nTYPE_GROUP\x10\n\x12\x10\n\x0cTYPE_MESSAGE\x10\x0b\x12\x0e\n\nTYPE_BYTES\x10\x0c\x12\x0f\n\x0bTYPE_UINT32\x10\r\x12\r\n\tTYPE_ENUM\x10\x0e\x12\x11\n\rTYPE_SFIXED32\x10\x0f\x12\x11\n\rTYPE_SFIXED64\x10\x10\x12\x0f\n\x0bTYPE_SINT32\x10\x11\x12\x0f\n\x0bTYPE_SINT64\x10\x12\"C\n\x05Label\x12\x12\n\x0eLABEL_OPTIONAL\x10\x01\x12\x12\n\x0eLABEL_REQUIRED\x10\x02\x12\x12\n\x0eLABEL_REPEATED\x10\x03\"T\n\x14OneofDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x07options\x18\x02 \x01(\x0b\x32\x1d.google.protobuf.OneofOptions\"\xa4\x02\n\x13\x45numDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x38\n\x05value\x18\x02 \x03(\x0b\x32).google.protobuf.EnumValueDescriptorProto\x12-\n\x07options\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.EnumOptions\x12N\n\x0ereserved_range\x18\x04 \x03(\x0b\x32\x36.google.protobuf.EnumDescriptorProto.EnumReservedRange\x12\x15\n\rreserved_name\x18\x05 \x03(\t\x1a/\n\x11\x45numReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"l\n\x18\x45numValueDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x02 \x01(\x05\x12\x32\n\x07options\x18\x03 \x01(\x0b\x32!.google.protobuf.EnumValueOptions\"\x90\x01\n\x16ServiceDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x36\n\x06method\x18\x02 \x03(\x0b\x32&.google.protobuf.MethodDescriptorProto\x12\x30\n\x07options\x18\x03 \x01(\x0b\x32\x1f.google.protobuf.ServiceOptions\"\xc1\x01\n\x15MethodDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ninput_type\x18\x02 \x01(\t\x12\x13\n\x0boutput_type\x18\x03 \x01(\t\x12/\n\x07options\x18\x04 \x01(\x0b\x32\x1e.google.protobuf.MethodOptions\x12\x1f\n\x10\x63lient_streaming\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10server_streaming\x18\x06 \x01(\x08:\x05\x66\x61lse\"\xa5\x06\n\x0b\x46ileOptions\x12\x14\n\x0cjava_package\x18\x01 \x01(\t\x12\x1c\n\x14java_outer_classname\x18\x08 \x01(\t\x12\"\n\x13java_multiple_files\x18\n \x01(\x08:\x05\x66\x61lse\x12)\n\x1djava_generate_equals_and_hash\x18\x14 \x01(\x08\x42\x02\x18\x01\x12%\n\x16java_string_check_utf8\x18\x1b \x01(\x08:\x05\x66\x61lse\x12\x46\n\x0coptimize_for\x18\t \x01(\x0e\x32).google.protobuf.FileOptions.OptimizeMode:\x05SPEED\x12\x12\n\ngo_package\x18\x0b \x01(\t\x12\"\n\x13\x63\x63_generic_services\x18\x10 \x01(\x08:\x05\x66\x61lse\x12$\n\x15java_generic_services\x18\x11 \x01(\x08:\x05\x66\x61lse\x12\"\n\x13py_generic_services\x18\x12 \x01(\x08:\x05\x66\x61lse\x12#\n\x14php_generic_services\x18* \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x17 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x10\x63\x63_enable_arenas\x18\x1f \x01(\x08:\x04true\x12\x19\n\x11objc_class_prefix\x18$ \x01(\t\x12\x18\n\x10\x63sharp_namespace\x18% \x01(\t\x12\x14\n\x0cswift_prefix\x18\' \x01(\t\x12\x18\n\x10php_class_prefix\x18( \x01(\t\x12\x15\n\rphp_namespace\x18) \x01(\t\x12\x1e\n\x16php_metadata_namespace\x18, \x01(\t\x12\x14\n\x0cruby_package\x18- \x01(\t\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\":\n\x0cOptimizeMode\x12\t\n\x05SPEED\x10\x01\x12\r\n\tCODE_SIZE\x10\x02\x12\x10\n\x0cLITE_RUNTIME\x10\x03*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08&\x10\'\"\x84\x02\n\x0eMessageOptions\x12&\n\x17message_set_wire_format\x18\x01 \x01(\x08:\x05\x66\x61lse\x12.\n\x1fno_standard_descriptor_accessor\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x11\n\tmap_entry\x18\x07 \x01(\x08\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n\"\xbe\x03\n\x0c\x46ieldOptions\x12:\n\x05\x63type\x18\x01 \x01(\x0e\x32#.google.protobuf.FieldOptions.CType:\x06STRING\x12\x0e\n\x06packed\x18\x02 \x01(\x08\x12?\n\x06jstype\x18\x06 \x01(\x0e\x32$.google.protobuf.FieldOptions.JSType:\tJS_NORMAL\x12\x13\n\x04lazy\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0funverified_lazy\x18\x0f \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x13\n\x04weak\x18\n \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\"/\n\x05\x43Type\x12\n\n\x06STRING\x10\x00\x12\x08\n\x04\x43ORD\x10\x01\x12\x10\n\x0cSTRING_PIECE\x10\x02\"5\n\x06JSType\x12\r\n\tJS_NORMAL\x10\x00\x12\r\n\tJS_STRING\x10\x01\x12\r\n\tJS_NUMBER\x10\x02*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05\"^\n\x0cOneofOptions\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x93\x01\n\x0b\x45numOptions\x12\x13\n\x0b\x61llow_alias\x18\x02 \x01(\x08\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x05\x10\x06\"}\n\x10\x45numValueOptions\x12\x19\n\ndeprecated\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"{\n\x0eServiceOptions\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xad\x02\n\rMethodOptions\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12_\n\x11idempotency_level\x18\" \x01(\x0e\x32/.google.protobuf.MethodOptions.IdempotencyLevel:\x13IDEMPOTENCY_UNKNOWN\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\"P\n\x10IdempotencyLevel\x12\x17\n\x13IDEMPOTENCY_UNKNOWN\x10\x00\x12\x13\n\x0fNO_SIDE_EFFECTS\x10\x01\x12\x0e\n\nIDEMPOTENT\x10\x02*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x9e\x02\n\x13UninterpretedOption\x12;\n\x04name\x18\x02 \x03(\x0b\x32-.google.protobuf.UninterpretedOption.NamePart\x12\x18\n\x10identifier_value\x18\x03 \x01(\t\x12\x1a\n\x12positive_int_value\x18\x04 \x01(\x04\x12\x1a\n\x12negative_int_value\x18\x05 \x01(\x03\x12\x14\n\x0c\x64ouble_value\x18\x06 \x01(\x01\x12\x14\n\x0cstring_value\x18\x07 \x01(\x0c\x12\x17\n\x0f\x61ggregate_value\x18\x08 \x01(\t\x1a\x33\n\x08NamePart\x12\x11\n\tname_part\x18\x01 \x02(\t\x12\x14\n\x0cis_extension\x18\x02 \x02(\x08\"\xd5\x01\n\x0eSourceCodeInfo\x12:\n\x08location\x18\x01 \x03(\x0b\x32(.google.protobuf.SourceCodeInfo.Location\x1a\x86\x01\n\x08Location\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x10\n\x04span\x18\x02 \x03(\x05\x42\x02\x10\x01\x12\x18\n\x10leading_comments\x18\x03 \x01(\t\x12\x19\n\x11trailing_comments\x18\x04 \x01(\t\x12!\n\x19leading_detached_comments\x18\x06 \x03(\t\"\xa7\x01\n\x11GeneratedCodeInfo\x12\x41\n\nannotation\x18\x01 \x03(\x0b\x32-.google.protobuf.GeneratedCodeInfo.Annotation\x1aO\n\nAnnotation\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x13\n\x0bsource_file\x18\x02 \x01(\t\x12\r\n\x05\x62\x65gin\x18\x03 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x04 \x01(\x05\x42~\n\x13\x63om.google.protobufB\x10\x44\x65scriptorProtosH\x01Z-google.golang.org/protobuf/types/descriptorpb\xf8\x01\x01\xa2\x02\x03GPB\xaa\x02\x1aGoogle.Protobuf.Reflection')

if _descriptor._USE_C_DESCRIPTORS == False:
    _FIELDDESCRIPTORPROTO_TYPE = _descriptor.EnumDescriptor(
        name='Type',
        full_name='google.protobuf.FieldDescriptorProto.Type',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='TYPE_DOUBLE', index=0, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_FLOAT', index=1, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_INT64', index=2, number=3,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_UINT64', index=3, number=4,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_INT32', index=4, number=5,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_FIXED64', index=5, number=6,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_FIXED32', index=6, number=7,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_BOOL', index=7, number=8,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_STRING', index=8, number=9,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_GROUP', index=9, number=10,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_MESSAGE', index=10, number=11,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_BYTES', index=11, number=12,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_UINT32', index=12, number=13,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_ENUM', index=13, number=14,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_SFIXED32', index=14, number=15,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_SFIXED64', index=15, number=16,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_SINT32', index=16, number=17,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='TYPE_SINT64', index=17, number=18,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_FIELDDESCRIPTORPROTO_TYPE)

    _FIELDDESCRIPTORPROTO_LABEL = _descriptor.EnumDescriptor(
        name='Label',
        full_name='google.protobuf.FieldDescriptorProto.Label',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='LABEL_OPTIONAL', index=0, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='LABEL_REQUIRED', index=1, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='LABEL_REPEATED', index=2, number=3,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_FIELDDESCRIPTORPROTO_LABEL)

    _FILEOPTIONS_OPTIMIZEMODE = _descriptor.EnumDescriptor(
        name='OptimizeMode',
        full_name='google.protobuf.FileOptions.OptimizeMode',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='SPEED', index=0, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='CODE_SIZE', index=1, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='LITE_RUNTIME', index=2, number=3,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_FILEOPTIONS_OPTIMIZEMODE)

    _FIELDOPTIONS_CTYPE = _descriptor.EnumDescriptor(
        name='CType',
        full_name='google.protobuf.FieldOptions.CType',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='STRING', index=0, number=0,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='CORD', index=1, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='STRING_PIECE', index=2, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_FIELDOPTIONS_CTYPE)

    _FIELDOPTIONS_JSTYPE = _descriptor.EnumDescriptor(
        name='JSType',
        full_name='google.protobuf.FieldOptions.JSType',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='JS_NORMAL', index=0, number=0,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='JS_STRING', index=1, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='JS_NUMBER', index=2, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_FIELDOPTIONS_JSTYPE)

    _METHODOPTIONS_IDEMPOTENCYLEVEL = _descriptor.EnumDescriptor(
        name='IdempotencyLevel',
        full_name='google.protobuf.MethodOptions.IdempotencyLevel',
        filename=None,
        file=DESCRIPTOR,
        create_key=_descriptor._internal_create_key,
        values=[
            _descriptor.EnumValueDescriptor(
                name='IDEMPOTENCY_UNKNOWN', index=0, number=0,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='NO_SIDE_EFFECTS', index=1, number=1,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
            _descriptor.EnumValueDescriptor(
                name='IDEMPOTENT', index=2, number=2,
                serialized_options=None,
                type=None,
                create_key=_descriptor._internal_create_key),
        ],
        containing_type=None,
        serialized_options=None,
    )
    _sym_db.RegisterEnumDescriptor(_METHODOPTIONS_IDEMPOTENCYLEVEL)

    _FILEDESCRIPTORSET = _descriptor.Descriptor(
        name='FileDescriptorSet',
        full_name='google.protobuf.FileDescriptorSet',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='file', full_name='google.protobuf.FileDescriptorSet.file', index=0,
                number=1, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _FILEDESCRIPTORPROTO = _descriptor.Descriptor(
        name='FileDescriptorProto',
        full_name='google.protobuf.FileDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.FileDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='package', full_name='google.protobuf.FileDescriptorProto.package', index=1,
                number=2, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='dependency', full_name='google.protobuf.FileDescriptorProto.dependency', index=2,
                number=3, type=9, cpp_type=9, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='public_dependency', full_name='google.protobuf.FileDescriptorProto.public_dependency', index=3,
                number=10, type=5, cpp_type=1, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='weak_dependency', full_name='google.protobuf.FileDescriptorProto.weak_dependency', index=4,
                number=11, type=5, cpp_type=1, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='message_type', full_name='google.protobuf.FileDescriptorProto.message_type', index=5,
                number=4, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='enum_type', full_name='google.protobuf.FileDescriptorProto.enum_type', index=6,
                number=5, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='service', full_name='google.protobuf.FileDescriptorProto.service', index=7,
                number=6, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='extension', full_name='google.protobuf.FileDescriptorProto.extension', index=8,
                number=7, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.FileDescriptorProto.options', index=9,
                number=8, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='source_code_info', full_name='google.protobuf.FileDescriptorProto.source_code_info', index=10,
                number=9, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='syntax', full_name='google.protobuf.FileDescriptorProto.syntax', index=11,
                number=12, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _DESCRIPTORPROTO_EXTENSIONRANGE = _descriptor.Descriptor(
        name='ExtensionRange',
        full_name='google.protobuf.DescriptorProto.ExtensionRange',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='start', full_name='google.protobuf.DescriptorProto.ExtensionRange.start', index=0,
                number=1, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='end', full_name='google.protobuf.DescriptorProto.ExtensionRange.end', index=1,
                number=2, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.DescriptorProto.ExtensionRange.options', index=2,
                number=3, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _DESCRIPTORPROTO_RESERVEDRANGE = _descriptor.Descriptor(
        name='ReservedRange',
        full_name='google.protobuf.DescriptorProto.ReservedRange',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='start', full_name='google.protobuf.DescriptorProto.ReservedRange.start', index=0,
                number=1, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='end', full_name='google.protobuf.DescriptorProto.ReservedRange.end', index=1,
                number=2, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _DESCRIPTORPROTO = _descriptor.Descriptor(
        name='DescriptorProto',
        full_name='google.protobuf.DescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.DescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='field', full_name='google.protobuf.DescriptorProto.field', index=1,
                number=2, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='extension', full_name='google.protobuf.DescriptorProto.extension', index=2,
                number=6, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='nested_type', full_name='google.protobuf.DescriptorProto.nested_type', index=3,
                number=3, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='enum_type', full_name='google.protobuf.DescriptorProto.enum_type', index=4,
                number=4, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='extension_range', full_name='google.protobuf.DescriptorProto.extension_range', index=5,
                number=5, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='oneof_decl', full_name='google.protobuf.DescriptorProto.oneof_decl', index=6,
                number=8, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.DescriptorProto.options', index=7,
                number=7, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='reserved_range', full_name='google.protobuf.DescriptorProto.reserved_range', index=8,
                number=9, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='reserved_name', full_name='google.protobuf.DescriptorProto.reserved_name', index=9,
                number=10, type=9, cpp_type=9, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[_DESCRIPTORPROTO_EXTENSIONRANGE, _DESCRIPTORPROTO_RESERVEDRANGE, ],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _EXTENSIONRANGEOPTIONS = _descriptor.Descriptor(
        name='ExtensionRangeOptions',
        full_name='google.protobuf.ExtensionRangeOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.ExtensionRangeOptions.uninterpreted_option',
                index=0,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _FIELDDESCRIPTORPROTO = _descriptor.Descriptor(
        name='FieldDescriptorProto',
        full_name='google.protobuf.FieldDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.FieldDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='number', full_name='google.protobuf.FieldDescriptorProto.number', index=1,
                number=3, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='label', full_name='google.protobuf.FieldDescriptorProto.label', index=2,
                number=4, type=14, cpp_type=8, label=1,
                has_default_value=False, default_value=1,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='type', full_name='google.protobuf.FieldDescriptorProto.type', index=3,
                number=5, type=14, cpp_type=8, label=1,
                has_default_value=False, default_value=1,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='type_name', full_name='google.protobuf.FieldDescriptorProto.type_name', index=4,
                number=6, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='extendee', full_name='google.protobuf.FieldDescriptorProto.extendee', index=5,
                number=2, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='default_value', full_name='google.protobuf.FieldDescriptorProto.default_value', index=6,
                number=7, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='oneof_index', full_name='google.protobuf.FieldDescriptorProto.oneof_index', index=7,
                number=9, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='json_name', full_name='google.protobuf.FieldDescriptorProto.json_name', index=8,
                number=10, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.FieldDescriptorProto.options', index=9,
                number=8, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='proto3_optional', full_name='google.protobuf.FieldDescriptorProto.proto3_optional', index=10,
                number=17, type=8, cpp_type=7, label=1,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
            _FIELDDESCRIPTORPROTO_TYPE,
            _FIELDDESCRIPTORPROTO_LABEL,
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _ONEOFDESCRIPTORPROTO = _descriptor.Descriptor(
        name='OneofDescriptorProto',
        full_name='google.protobuf.OneofDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.OneofDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.OneofDescriptorProto.options', index=1,
                number=2, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE = _descriptor.Descriptor(
        name='EnumReservedRange',
        full_name='google.protobuf.EnumDescriptorProto.EnumReservedRange',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='start', full_name='google.protobuf.EnumDescriptorProto.EnumReservedRange.start', index=0,
                number=1, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='end', full_name='google.protobuf.EnumDescriptorProto.EnumReservedRange.end', index=1,
                number=2, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _ENUMDESCRIPTORPROTO = _descriptor.Descriptor(
        name='EnumDescriptorProto',
        full_name='google.protobuf.EnumDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.EnumDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='value', full_name='google.protobuf.EnumDescriptorProto.value', index=1,
                number=2, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.EnumDescriptorProto.options', index=2,
                number=3, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='reserved_range', full_name='google.protobuf.EnumDescriptorProto.reserved_range', index=3,
                number=4, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='reserved_name', full_name='google.protobuf.EnumDescriptorProto.reserved_name', index=4,
                number=5, type=9, cpp_type=9, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[_ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE, ],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _ENUMVALUEDESCRIPTORPROTO = _descriptor.Descriptor(
        name='EnumValueDescriptorProto',
        full_name='google.protobuf.EnumValueDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.EnumValueDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='number', full_name='google.protobuf.EnumValueDescriptorProto.number', index=1,
                number=2, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.EnumValueDescriptorProto.options', index=2,
                number=3, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _SERVICEDESCRIPTORPROTO = _descriptor.Descriptor(
        name='ServiceDescriptorProto',
        full_name='google.protobuf.ServiceDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.ServiceDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='method', full_name='google.protobuf.ServiceDescriptorProto.method', index=1,
                number=2, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.ServiceDescriptorProto.options', index=2,
                number=3, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _METHODDESCRIPTORPROTO = _descriptor.Descriptor(
        name='MethodDescriptorProto',
        full_name='google.protobuf.MethodDescriptorProto',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.MethodDescriptorProto.name', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='input_type', full_name='google.protobuf.MethodDescriptorProto.input_type', index=1,
                number=2, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='output_type', full_name='google.protobuf.MethodDescriptorProto.output_type', index=2,
                number=3, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='options', full_name='google.protobuf.MethodDescriptorProto.options', index=3,
                number=4, type=11, cpp_type=10, label=1,
                has_default_value=False, default_value=None,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='client_streaming', full_name='google.protobuf.MethodDescriptorProto.client_streaming', index=4,
                number=5, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='server_streaming', full_name='google.protobuf.MethodDescriptorProto.server_streaming', index=5,
                number=6, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _FILEOPTIONS = _descriptor.Descriptor(
        name='FileOptions',
        full_name='google.protobuf.FileOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='java_package', full_name='google.protobuf.FileOptions.java_package', index=0,
                number=1, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='java_outer_classname', full_name='google.protobuf.FileOptions.java_outer_classname', index=1,
                number=8, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='java_multiple_files', full_name='google.protobuf.FileOptions.java_multiple_files', index=2,
                number=10, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='java_generate_equals_and_hash',
                full_name='google.protobuf.FileOptions.java_generate_equals_and_hash', index=3,
                number=20, type=8, cpp_type=7, label=1,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='java_string_check_utf8', full_name='google.protobuf.FileOptions.java_string_check_utf8', index=4,
                number=27, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='optimize_for', full_name='google.protobuf.FileOptions.optimize_for', index=5,
                number=9, type=14, cpp_type=8, label=1,
                has_default_value=True, default_value=1,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='go_package', full_name='google.protobuf.FileOptions.go_package', index=6,
                number=11, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='cc_generic_services', full_name='google.protobuf.FileOptions.cc_generic_services', index=7,
                number=16, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='java_generic_services', full_name='google.protobuf.FileOptions.java_generic_services', index=8,
                number=17, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='py_generic_services', full_name='google.protobuf.FileOptions.py_generic_services', index=9,
                number=18, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='php_generic_services', full_name='google.protobuf.FileOptions.php_generic_services', index=10,
                number=42, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.FileOptions.deprecated', index=11,
                number=23, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='cc_enable_arenas', full_name='google.protobuf.FileOptions.cc_enable_arenas', index=12,
                number=31, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=True,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='objc_class_prefix', full_name='google.protobuf.FileOptions.objc_class_prefix', index=13,
                number=36, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='csharp_namespace', full_name='google.protobuf.FileOptions.csharp_namespace', index=14,
                number=37, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='swift_prefix', full_name='google.protobuf.FileOptions.swift_prefix', index=15,
                number=39, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='php_class_prefix', full_name='google.protobuf.FileOptions.php_class_prefix', index=16,
                number=40, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='php_namespace', full_name='google.protobuf.FileOptions.php_namespace', index=17,
                number=41, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='php_metadata_namespace', full_name='google.protobuf.FileOptions.php_metadata_namespace', index=18,
                number=44, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='ruby_package', full_name='google.protobuf.FileOptions.ruby_package', index=19,
                number=45, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.FileOptions.uninterpreted_option', index=20,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
            _FILEOPTIONS_OPTIMIZEMODE,
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _MESSAGEOPTIONS = _descriptor.Descriptor(
        name='MessageOptions',
        full_name='google.protobuf.MessageOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='message_set_wire_format', full_name='google.protobuf.MessageOptions.message_set_wire_format',
                index=0,
                number=1, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='no_standard_descriptor_accessor',
                full_name='google.protobuf.MessageOptions.no_standard_descriptor_accessor', index=1,
                number=2, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.MessageOptions.deprecated', index=2,
                number=3, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='map_entry', full_name='google.protobuf.MessageOptions.map_entry', index=3,
                number=7, type=8, cpp_type=7, label=1,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.MessageOptions.uninterpreted_option', index=4,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _FIELDOPTIONS = _descriptor.Descriptor(
        name='FieldOptions',
        full_name='google.protobuf.FieldOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='ctype', full_name='google.protobuf.FieldOptions.ctype', index=0,
                number=1, type=14, cpp_type=8, label=1,
                has_default_value=True, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='packed', full_name='google.protobuf.FieldOptions.packed', index=1,
                number=2, type=8, cpp_type=7, label=1,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='jstype', full_name='google.protobuf.FieldOptions.jstype', index=2,
                number=6, type=14, cpp_type=8, label=1,
                has_default_value=True, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='lazy', full_name='google.protobuf.FieldOptions.lazy', index=3,
                number=5, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='unverified_lazy', full_name='google.protobuf.FieldOptions.unverified_lazy', index=4,
                number=15, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.FieldOptions.deprecated', index=5,
                number=3, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='weak', full_name='google.protobuf.FieldOptions.weak', index=6,
                number=10, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.FieldOptions.uninterpreted_option', index=7,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
            _FIELDOPTIONS_CTYPE,
            _FIELDOPTIONS_JSTYPE,
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _ONEOFOPTIONS = _descriptor.Descriptor(
        name='OneofOptions',
        full_name='google.protobuf.OneofOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.OneofOptions.uninterpreted_option', index=0,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _ENUMOPTIONS = _descriptor.Descriptor(
        name='EnumOptions',
        full_name='google.protobuf.EnumOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='allow_alias', full_name='google.protobuf.EnumOptions.allow_alias', index=0,
                number=2, type=8, cpp_type=7, label=1,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.EnumOptions.deprecated', index=1,
                number=3, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.EnumOptions.uninterpreted_option', index=2,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _ENUMVALUEOPTIONS = _descriptor.Descriptor(
        name='EnumValueOptions',
        full_name='google.protobuf.EnumValueOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.EnumValueOptions.deprecated', index=0,
                number=1, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.EnumValueOptions.uninterpreted_option', index=1,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _SERVICEOPTIONS = _descriptor.Descriptor(
        name='ServiceOptions',
        full_name='google.protobuf.ServiceOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.ServiceOptions.deprecated', index=0,
                number=33, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.ServiceOptions.uninterpreted_option', index=1,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _METHODOPTIONS = _descriptor.Descriptor(
        name='MethodOptions',
        full_name='google.protobuf.MethodOptions',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='deprecated', full_name='google.protobuf.MethodOptions.deprecated', index=0,
                number=33, type=8, cpp_type=7, label=1,
                has_default_value=True, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='idempotency_level', full_name='google.protobuf.MethodOptions.idempotency_level', index=1,
                number=34, type=14, cpp_type=8, label=1,
                has_default_value=True, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='uninterpreted_option', full_name='google.protobuf.MethodOptions.uninterpreted_option', index=2,
                number=999, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
            _METHODOPTIONS_IDEMPOTENCYLEVEL,
        ],
        serialized_options=None,
        is_extendable=True,
        syntax='proto2',
        extension_ranges=[(1000, *********), ],
        oneofs=[
        ],
    )

    _UNINTERPRETEDOPTION_NAMEPART = _descriptor.Descriptor(
        name='NamePart',
        full_name='google.protobuf.UninterpretedOption.NamePart',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name_part', full_name='google.protobuf.UninterpretedOption.NamePart.name_part', index=0,
                number=1, type=9, cpp_type=9, label=2,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='is_extension', full_name='google.protobuf.UninterpretedOption.NamePart.is_extension', index=1,
                number=2, type=8, cpp_type=7, label=2,
                has_default_value=False, default_value=False,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _UNINTERPRETEDOPTION = _descriptor.Descriptor(
        name='UninterpretedOption',
        full_name='google.protobuf.UninterpretedOption',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='name', full_name='google.protobuf.UninterpretedOption.name', index=0,
                number=2, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='identifier_value', full_name='google.protobuf.UninterpretedOption.identifier_value', index=1,
                number=3, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='positive_int_value', full_name='google.protobuf.UninterpretedOption.positive_int_value', index=2,
                number=4, type=4, cpp_type=4, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='negative_int_value', full_name='google.protobuf.UninterpretedOption.negative_int_value', index=3,
                number=5, type=3, cpp_type=2, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='double_value', full_name='google.protobuf.UninterpretedOption.double_value', index=4,
                number=6, type=1, cpp_type=5, label=1,
                has_default_value=False, default_value=float(0),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='string_value', full_name='google.protobuf.UninterpretedOption.string_value', index=5,
                number=7, type=12, cpp_type=9, label=1,
                has_default_value=False, default_value=b"",
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='aggregate_value', full_name='google.protobuf.UninterpretedOption.aggregate_value', index=6,
                number=8, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[_UNINTERPRETEDOPTION_NAMEPART, ],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _SOURCECODEINFO_LOCATION = _descriptor.Descriptor(
        name='Location',
        full_name='google.protobuf.SourceCodeInfo.Location',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='path', full_name='google.protobuf.SourceCodeInfo.Location.path', index=0,
                number=1, type=5, cpp_type=1, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='span', full_name='google.protobuf.SourceCodeInfo.Location.span', index=1,
                number=2, type=5, cpp_type=1, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='leading_comments', full_name='google.protobuf.SourceCodeInfo.Location.leading_comments', index=2,
                number=3, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='trailing_comments', full_name='google.protobuf.SourceCodeInfo.Location.trailing_comments',
                index=3,
                number=4, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='leading_detached_comments',
                full_name='google.protobuf.SourceCodeInfo.Location.leading_detached_comments', index=4,
                number=6, type=9, cpp_type=9, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _SOURCECODEINFO = _descriptor.Descriptor(
        name='SourceCodeInfo',
        full_name='google.protobuf.SourceCodeInfo',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='location', full_name='google.protobuf.SourceCodeInfo.location', index=0,
                number=1, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[_SOURCECODEINFO_LOCATION, ],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _GENERATEDCODEINFO_ANNOTATION = _descriptor.Descriptor(
        name='Annotation',
        full_name='google.protobuf.GeneratedCodeInfo.Annotation',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='path', full_name='google.protobuf.GeneratedCodeInfo.Annotation.path', index=0,
                number=1, type=5, cpp_type=1, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='source_file', full_name='google.protobuf.GeneratedCodeInfo.Annotation.source_file', index=1,
                number=2, type=9, cpp_type=9, label=1,
                has_default_value=False, default_value=b"".decode('utf-8'),
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='begin', full_name='google.protobuf.GeneratedCodeInfo.Annotation.begin', index=2,
                number=3, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
            _descriptor.FieldDescriptor(
                name='end', full_name='google.protobuf.GeneratedCodeInfo.Annotation.end', index=3,
                number=4, type=5, cpp_type=1, label=1,
                has_default_value=False, default_value=0,
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _GENERATEDCODEINFO = _descriptor.Descriptor(
        name='GeneratedCodeInfo',
        full_name='google.protobuf.GeneratedCodeInfo',
        filename=None,
        file=DESCRIPTOR,
        containing_type=None,
        create_key=_descriptor._internal_create_key,
        fields=[
            _descriptor.FieldDescriptor(
                name='annotation', full_name='google.protobuf.GeneratedCodeInfo.annotation', index=0,
                number=1, type=11, cpp_type=10, label=3,
                has_default_value=False, default_value=[],
                message_type=None, enum_type=None, containing_type=None,
                is_extension=False, extension_scope=None,
                serialized_options=None, file=DESCRIPTOR, create_key=_descriptor._internal_create_key),
        ],
        extensions=[
        ],
        nested_types=[_GENERATEDCODEINFO_ANNOTATION, ],
        enum_types=[
        ],
        serialized_options=None,
        is_extendable=False,
        syntax='proto2',
        extension_ranges=[],
        oneofs=[
        ],
    )

    _FILEDESCRIPTORSET.fields_by_name['file'].message_type = _FILEDESCRIPTORPROTO
    _FILEDESCRIPTORPROTO.fields_by_name['message_type'].message_type = _DESCRIPTORPROTO
    _FILEDESCRIPTORPROTO.fields_by_name['enum_type'].message_type = _ENUMDESCRIPTORPROTO
    _FILEDESCRIPTORPROTO.fields_by_name['service'].message_type = _SERVICEDESCRIPTORPROTO
    _FILEDESCRIPTORPROTO.fields_by_name['extension'].message_type = _FIELDDESCRIPTORPROTO
    _FILEDESCRIPTORPROTO.fields_by_name['options'].message_type = _FILEOPTIONS
    _FILEDESCRIPTORPROTO.fields_by_name['source_code_info'].message_type = _SOURCECODEINFO
    _DESCRIPTORPROTO_EXTENSIONRANGE.fields_by_name['options'].message_type = _EXTENSIONRANGEOPTIONS
    _DESCRIPTORPROTO_EXTENSIONRANGE.containing_type = _DESCRIPTORPROTO
    _DESCRIPTORPROTO_RESERVEDRANGE.containing_type = _DESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['field'].message_type = _FIELDDESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['extension'].message_type = _FIELDDESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['nested_type'].message_type = _DESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['enum_type'].message_type = _ENUMDESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['extension_range'].message_type = _DESCRIPTORPROTO_EXTENSIONRANGE
    _DESCRIPTORPROTO.fields_by_name['oneof_decl'].message_type = _ONEOFDESCRIPTORPROTO
    _DESCRIPTORPROTO.fields_by_name['options'].message_type = _MESSAGEOPTIONS
    _DESCRIPTORPROTO.fields_by_name['reserved_range'].message_type = _DESCRIPTORPROTO_RESERVEDRANGE
    _EXTENSIONRANGEOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _FIELDDESCRIPTORPROTO.fields_by_name['label'].enum_type = _FIELDDESCRIPTORPROTO_LABEL
    _FIELDDESCRIPTORPROTO.fields_by_name['type'].enum_type = _FIELDDESCRIPTORPROTO_TYPE
    _FIELDDESCRIPTORPROTO.fields_by_name['options'].message_type = _FIELDOPTIONS
    _FIELDDESCRIPTORPROTO_TYPE.containing_type = _FIELDDESCRIPTORPROTO
    _FIELDDESCRIPTORPROTO_LABEL.containing_type = _FIELDDESCRIPTORPROTO
    _ONEOFDESCRIPTORPROTO.fields_by_name['options'].message_type = _ONEOFOPTIONS
    _ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE.containing_type = _ENUMDESCRIPTORPROTO
    _ENUMDESCRIPTORPROTO.fields_by_name['value'].message_type = _ENUMVALUEDESCRIPTORPROTO
    _ENUMDESCRIPTORPROTO.fields_by_name['options'].message_type = _ENUMOPTIONS
    _ENUMDESCRIPTORPROTO.fields_by_name['reserved_range'].message_type = _ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE
    _ENUMVALUEDESCRIPTORPROTO.fields_by_name['options'].message_type = _ENUMVALUEOPTIONS
    _SERVICEDESCRIPTORPROTO.fields_by_name['method'].message_type = _METHODDESCRIPTORPROTO
    _SERVICEDESCRIPTORPROTO.fields_by_name['options'].message_type = _SERVICEOPTIONS
    _METHODDESCRIPTORPROTO.fields_by_name['options'].message_type = _METHODOPTIONS
    _FILEOPTIONS.fields_by_name['optimize_for'].enum_type = _FILEOPTIONS_OPTIMIZEMODE
    _FILEOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _FILEOPTIONS_OPTIMIZEMODE.containing_type = _FILEOPTIONS
    _MESSAGEOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _FIELDOPTIONS.fields_by_name['ctype'].enum_type = _FIELDOPTIONS_CTYPE
    _FIELDOPTIONS.fields_by_name['jstype'].enum_type = _FIELDOPTIONS_JSTYPE
    _FIELDOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _FIELDOPTIONS_CTYPE.containing_type = _FIELDOPTIONS
    _FIELDOPTIONS_JSTYPE.containing_type = _FIELDOPTIONS
    _ONEOFOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _ENUMOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _ENUMVALUEOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _SERVICEOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _METHODOPTIONS.fields_by_name['idempotency_level'].enum_type = _METHODOPTIONS_IDEMPOTENCYLEVEL
    _METHODOPTIONS.fields_by_name['uninterpreted_option'].message_type = _UNINTERPRETEDOPTION
    _METHODOPTIONS_IDEMPOTENCYLEVEL.containing_type = _METHODOPTIONS
    _UNINTERPRETEDOPTION_NAMEPART.containing_type = _UNINTERPRETEDOPTION
    _UNINTERPRETEDOPTION.fields_by_name['name'].message_type = _UNINTERPRETEDOPTION_NAMEPART
    _SOURCECODEINFO_LOCATION.containing_type = _SOURCECODEINFO
    _SOURCECODEINFO.fields_by_name['location'].message_type = _SOURCECODEINFO_LOCATION
    _GENERATEDCODEINFO_ANNOTATION.containing_type = _GENERATEDCODEINFO
    _GENERATEDCODEINFO.fields_by_name['annotation'].message_type = _GENERATEDCODEINFO_ANNOTATION
    DESCRIPTOR.message_types_by_name['FileDescriptorSet'] = _FILEDESCRIPTORSET
    DESCRIPTOR.message_types_by_name['FileDescriptorProto'] = _FILEDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['DescriptorProto'] = _DESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['ExtensionRangeOptions'] = _EXTENSIONRANGEOPTIONS
    DESCRIPTOR.message_types_by_name['FieldDescriptorProto'] = _FIELDDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['OneofDescriptorProto'] = _ONEOFDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['EnumDescriptorProto'] = _ENUMDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['EnumValueDescriptorProto'] = _ENUMVALUEDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['ServiceDescriptorProto'] = _SERVICEDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['MethodDescriptorProto'] = _METHODDESCRIPTORPROTO
    DESCRIPTOR.message_types_by_name['FileOptions'] = _FILEOPTIONS
    DESCRIPTOR.message_types_by_name['MessageOptions'] = _MESSAGEOPTIONS
    DESCRIPTOR.message_types_by_name['FieldOptions'] = _FIELDOPTIONS
    DESCRIPTOR.message_types_by_name['OneofOptions'] = _ONEOFOPTIONS
    DESCRIPTOR.message_types_by_name['EnumOptions'] = _ENUMOPTIONS
    DESCRIPTOR.message_types_by_name['EnumValueOptions'] = _ENUMVALUEOPTIONS
    DESCRIPTOR.message_types_by_name['ServiceOptions'] = _SERVICEOPTIONS
    DESCRIPTOR.message_types_by_name['MethodOptions'] = _METHODOPTIONS
    DESCRIPTOR.message_types_by_name['UninterpretedOption'] = _UNINTERPRETEDOPTION
    DESCRIPTOR.message_types_by_name['SourceCodeInfo'] = _SOURCECODEINFO
    DESCRIPTOR.message_types_by_name['GeneratedCodeInfo'] = _GENERATEDCODEINFO
    _sym_db.RegisterFileDescriptor(DESCRIPTOR)

else:
    _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'google.protobuf.descriptor_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _FILEDESCRIPTORSET._serialized_start = 53
    _FILEDESCRIPTORSET._serialized_end = 124
    _FILEDESCRIPTORPROTO._serialized_start = 127
    _FILEDESCRIPTORPROTO._serialized_end = 602
    _DESCRIPTORPROTO._serialized_start = 605
    _DESCRIPTORPROTO._serialized_end = 1286
    _DESCRIPTORPROTO_EXTENSIONRANGE._serialized_start = 1140
    _DESCRIPTORPROTO_EXTENSIONRANGE._serialized_end = 1241
    _DESCRIPTORPROTO_RESERVEDRANGE._serialized_start = 1243
    _DESCRIPTORPROTO_RESERVEDRANGE._serialized_end = 1286
    _EXTENSIONRANGEOPTIONS._serialized_start = 1288
    _EXTENSIONRANGEOPTIONS._serialized_end = 1391
    _FIELDDESCRIPTORPROTO._serialized_start = 1394
    _FIELDDESCRIPTORPROTO._serialized_end = 2119
    _FIELDDESCRIPTORPROTO_TYPE._serialized_start = 1740
    _FIELDDESCRIPTORPROTO_TYPE._serialized_end = 2050
    _FIELDDESCRIPTORPROTO_LABEL._serialized_start = 2052
    _FIELDDESCRIPTORPROTO_LABEL._serialized_end = 2119
    _ONEOFDESCRIPTORPROTO._serialized_start = 2121
    _ONEOFDESCRIPTORPROTO._serialized_end = 2205
    _ENUMDESCRIPTORPROTO._serialized_start = 2208
    _ENUMDESCRIPTORPROTO._serialized_end = 2500
    _ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE._serialized_start = 2453
    _ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE._serialized_end = 2500
    _ENUMVALUEDESCRIPTORPROTO._serialized_start = 2502
    _ENUMVALUEDESCRIPTORPROTO._serialized_end = 2610
    _SERVICEDESCRIPTORPROTO._serialized_start = 2613
    _SERVICEDESCRIPTORPROTO._serialized_end = 2757
    _METHODDESCRIPTORPROTO._serialized_start = 2760
    _METHODDESCRIPTORPROTO._serialized_end = 2953
    _FILEOPTIONS._serialized_start = 2956
    _FILEOPTIONS._serialized_end = 3761
    _FILEOPTIONS_OPTIMIZEMODE._serialized_start = 3686
    _FILEOPTIONS_OPTIMIZEMODE._serialized_end = 3744
    _MESSAGEOPTIONS._serialized_start = 3764
    _MESSAGEOPTIONS._serialized_end = 4024
    _FIELDOPTIONS._serialized_start = 4027
    _FIELDOPTIONS._serialized_end = 4473
    _FIELDOPTIONS_CTYPE._serialized_start = 4354
    _FIELDOPTIONS_CTYPE._serialized_end = 4401
    _FIELDOPTIONS_JSTYPE._serialized_start = 4403
    _FIELDOPTIONS_JSTYPE._serialized_end = 4456
    _ONEOFOPTIONS._serialized_start = 4475
    _ONEOFOPTIONS._serialized_end = 4569
    _ENUMOPTIONS._serialized_start = 4572
    _ENUMOPTIONS._serialized_end = 4719
    _ENUMVALUEOPTIONS._serialized_start = 4721
    _ENUMVALUEOPTIONS._serialized_end = 4846
    _SERVICEOPTIONS._serialized_start = 4848
    _SERVICEOPTIONS._serialized_end = 4971
    _METHODOPTIONS._serialized_start = 4974
    _METHODOPTIONS._serialized_end = 5275
    _METHODOPTIONS_IDEMPOTENCYLEVEL._serialized_start = 5184
    _METHODOPTIONS_IDEMPOTENCYLEVEL._serialized_end = 5264
    _UNINTERPRETEDOPTION._serialized_start = 5278
    _UNINTERPRETEDOPTION._serialized_end = 5564
    _UNINTERPRETEDOPTION_NAMEPART._serialized_start = 5513
    _UNINTERPRETEDOPTION_NAMEPART._serialized_end = 5564
    _SOURCECODEINFO._serialized_start = 5567
    _SOURCECODEINFO._serialized_end = 5780
    _SOURCECODEINFO_LOCATION._serialized_start = 5646
    _SOURCECODEINFO_LOCATION._serialized_end = 5780
    _GENERATEDCODEINFO._serialized_start = 5783
    _GENERATEDCODEINFO._serialized_end = 5950
    _GENERATEDCODEINFO_ANNOTATION._serialized_start = 5871
    _GENERATEDCODEINFO_ANNOTATION._serialized_end = 5950
# @@protoc_insertion_point(module_scope)
