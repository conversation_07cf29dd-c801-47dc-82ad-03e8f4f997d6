#!/usr/bin/env python3
"""
测试下载API端点
"""
import requests
import json

def test_download_endpoint():
    """测试下载端点是否可访问"""
    print("=" * 60)
    print("测试下载API端点")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 测试1: 检查API根路径
    print("\n1. 测试API根路径...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试2: 检查API文档
    print("\n2. 检查API文档...")
    try:
        response = requests.get(f"{base_url}/docs")
        print(f"状态码: {response.status_code}")
        print("API文档可访问" if response.status_code == 200 else "API文档不可访问")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试3: 尝试未认证的下载请求
    print("\n3. 测试未认证的下载请求...")
    try:
        download_url = f"{base_url}/api/wechat/download-data/1"
        params = {
            'start_date': '2025-06-18',
            'end_date': '2025-07-17',
            'busi': 3,
            'tmpl': 19
        }
        response = requests.get(download_url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text[:200]}...")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试4: 检查wechat路由是否存在
    print("\n4. 检查微信路由...")
    try:
        # 尝试访问一个不需要认证的端点（如果有的话）
        response = requests.get(f"{base_url}/api/wechat/")
        print(f"状态码: {response.status_code}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_download_endpoint()
