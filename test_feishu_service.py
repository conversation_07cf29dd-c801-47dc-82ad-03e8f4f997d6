#!/usr/bin/env python3
"""
飞书服务测试脚本
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

from app.services.feishu_service import FeishuService
from app.services.data_converter import WeChatDataConverter

def test_feishu_service():
    """测试飞书服务基本功能"""
    print("=" * 60)
    print("飞书服务测试")
    print("=" * 60)
    
    # 检查环境变量
    app_id = os.getenv("FEISHU_APP_ID")
    app_secret = os.getenv("FEISHU_APP_SECRET")
    
    if not app_id or not app_secret:
        print("❌ 请先设置环境变量 FEISHU_APP_ID 和 FEISHU_APP_SECRET")
        print("   可以在 .env 文件中设置这些变量")
        return False
    
    print(f"✅ 飞书应用ID: {app_id[:8]}...")
    print(f"✅ 飞书应用密钥: {app_secret[:8]}...")
    
    try:
        # 初始化飞书服务
        print("\n🔄 初始化飞书服务...")
        feishu_service = FeishuService()
        print("✅ 飞书服务初始化成功")
        
        # 测试创建多维表格
        print("\n🔄 测试创建多维表格...")
        import time
        bitable_name = "测试多维表格_" + str(int(time.time()))
        app_token = feishu_service.create_bitable(bitable_name)
        
        if app_token:
            print(f"✅ 多维表格创建成功: {app_token}")
            
            # 测试创建数据表
            print("\n🔄 测试创建数据表...")
            table_id = feishu_service.create_table(app_token, "测试数据表")
            
            if table_id:
                print(f"✅ 数据表创建成功: {table_id}")
                
                # 测试创建记录
                print("\n🔄 测试创建记录...")
                test_records = [
                    {
                        "日期": "2024-01-01",
                        "新增用户数": 100,
                        "取消关注用户数": 10,
                        "净增长": 90,
                        "累计用户数": 1000
                    },
                    {
                        "日期": "2024-01-02", 
                        "新增用户数": 120,
                        "取消关注用户数": 15,
                        "净增长": 105,
                        "累计用户数": 1105
                    }
                ]
                
                record_ids = feishu_service.batch_create_records(app_token, table_id, test_records)
                
                if record_ids:
                    print(f"✅ 记录创建成功: {len(record_ids)} 条记录")
                    print(f"   记录ID: {record_ids}")
                    
                    # 测试获取记录
                    print("\n🔄 测试获取记录...")
                    records = feishu_service.list_records(app_token, table_id)
                    
                    if records:
                        print(f"✅ 获取记录成功: {len(records)} 条记录")
                        for i, record in enumerate(records[:2]):  # 只显示前2条
                            print(f"   记录{i+1}: {record.get('fields', {})}")
                    else:
                        print("❌ 获取记录失败")
                else:
                    print("❌ 记录创建失败")
            else:
                print("❌ 数据表创建失败")
        else:
            print("❌ 多维表格创建失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_converter():
    """测试数据转换器"""
    print("\n" + "=" * 60)
    print("数据转换器测试")
    print("=" * 60)
    
    try:
        # 创建测试Excel数据
        import pandas as pd
        from io import BytesIO
        
        # 创建用户概况数据
        user_data = {
            "日期": ["2024-01-01", "2024-01-02", "2024-01-03"],
            "新增用户": [100, 120, 80],
            "取消关注用户": [10, 15, 5],
            "净增长": [90, 105, 75],
            "累计用户": [1000, 1105, 1180]
        }
        
        # 创建图文分析数据
        article_data = {
            "标题": ["测试文章1", "测试文章2", "测试文章3"],
            "阅读数": [1000, 1500, 800],
            "点赞数": [50, 75, 40],
            "分享数": [20, 30, 15],
            "发布时间": ["2024-01-01 10:00:00", "2024-01-02 14:00:00", "2024-01-03 16:00:00"]
        }
        
        # 创建Excel文件
        excel_buffer = BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            pd.DataFrame(user_data).to_excel(writer, sheet_name='用户概况', index=False)
            pd.DataFrame(article_data).to_excel(writer, sheet_name='图文分析', index=False)
        
        excel_data = excel_buffer.getvalue()
        print(f"✅ 测试Excel数据创建成功: {len(excel_data)} bytes")
        
        # 测试解析Excel数据
        print("\n🔄 测试解析Excel数据...")
        parsed_data = WeChatDataConverter.parse_excel_data(excel_data)
        
        if parsed_data:
            print("✅ Excel数据解析成功")
            print(f"   用户概况数据: {len(parsed_data['user_summary'])} 条")
            print(f"   图文分析数据: {len(parsed_data['article_summary'])} 条")
            print(f"   发现的sheet: {parsed_data['raw_sheets']}")
            
            # 测试准备飞书记录格式
            print("\n🔄 测试准备飞书记录格式...")
            user_records = WeChatDataConverter.prepare_feishu_records(
                "user_summary", 
                parsed_data["user_summary"]
            )
            
            article_records = WeChatDataConverter.prepare_feishu_records(
                "article_summary",
                parsed_data["article_summary"]
            )
            
            print(f"✅ 飞书格式转换成功")
            print(f"   用户记录: {len(user_records)} 条")
            print(f"   图文记录: {len(article_records)} 条")
            
            if user_records:
                print(f"   用户记录示例: {user_records[0]}")
            if article_records:
                print(f"   图文记录示例: {article_records[0]}")
                
        else:
            print("❌ Excel数据解析失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 数据转换器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("飞书多维表格服务测试工具")
    print("注意: 此测试需要有效的飞书应用凭证")
    
    # 测试数据转换器（不需要飞书凭证）
    converter_success = test_data_converter()
    
    # 测试飞书服务（需要飞书凭证）
    service_success = test_feishu_service()
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"数据转换器测试: {'✅ 通过' if converter_success else '❌ 失败'}")
    print(f"飞书服务测试: {'✅ 通过' if service_success else '❌ 失败'}")
    
    if converter_success and service_success:
        print("\n🎉 所有测试通过！飞书多维表格服务可以正常使用。")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查配置和网络连接。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
