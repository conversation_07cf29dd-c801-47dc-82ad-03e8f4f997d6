#!/usr/bin/env python3
"""
测试微信公众号二维码获取功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_service import WeChatMPService

async def test_qrcode_generation():
    """测试二维码生成功能"""
    service = WeChatMPService()
    
    try:
        print("=" * 50)
        print("开始测试微信公众号二维码获取功能")
        print("=" * 50)
        
        # 测试1: 获取二维码
        print("\n1. 测试获取二维码...")
        qr_data = await service.get_login_qrcode()
        
        if qr_data:
            print(f"✓ 成功获取二维码，数据长度: {len(qr_data)}")
            print(f"✓ 数据格式: {'正确' if qr_data.startswith('data:image/png;base64,') else '错误'}")
            
            # 保存二维码到文件用于查看
            import base64
            if qr_data.startswith('data:image/png;base64,'):
                qr_base64 = qr_data.replace('data:image/png;base64,', '')
                with open('qrcode_test.png', 'wb') as f:
                    f.write(base64.b64decode(qr_base64))
                print("✓ 二维码已保存到 qrcode_test.png")
        else:
            print("✗ 获取二维码失败")
            return False
        
        # 测试2: 检查登录状态和等待登录功能
        print("\n2. 测试检查登录状态...")
        login_status = await service.check_login_status(wait_for_redirect=False)
        print(f"当前登录状态: {'已登录' if login_status else '未登录'}")
        
        # 询问用户是否要测试登录等待功能
        print("\n2.1 测试登录等待功能（可选）")
        print("提示：这将等待您扫码登录，您可以:")
        print("  - 在手机上扫描生成的二维码来测试登录流程")
        print("  - 或者直接跳过这个测试")
        
        # 由于这是自动化测试，我们不会真的等待用户扫码，只演示功能
        print("跳过真实扫码测试，继续其他测试...")
        
        # 测试3: 刷新二维码
        print("\n3. 测试刷新二维码...")
        await asyncio.sleep(2)  # 等待一下
        new_qr_data = await service.refresh_qrcode()
        
        if new_qr_data:
            print(f"✓ 成功刷新二维码，数据长度: {len(new_qr_data)}")
            
            # 保存刷新后的二维码
            if new_qr_data.startswith('data:image/png;base64,'):
                qr_base64 = new_qr_data.replace('data:image/png;base64,', '')
                with open('qrcode_refreshed.png', 'wb') as f:
                    f.write(base64.b64decode(qr_base64))
                print("✓ 刷新后的二维码已保存到 qrcode_refreshed.png")
        else:
            print("✗ 刷新二维码失败")
        
        # 测试4: 多次获取二维码测试稳定性
        print("\n4. 测试多次获取二维码的稳定性...")
        success_count = 0
        total_tests = 3
        
        for i in range(total_tests):
            print(f"第 {i+1} 次测试...")
            # 先关闭当前连接
            await service.close()
            await asyncio.sleep(1)
            
            # 重新获取
            qr_data = await service.get_login_qrcode()
            if qr_data and len(qr_data) > 100:  # 基本的数据完整性检查
                success_count += 1
                print(f"  ✓ 第 {i+1} 次成功")
            else:
                print(f"  ✗ 第 {i+1} 次失败")
        
        print(f"\n稳定性测试结果: {success_count}/{total_tests} 成功")
        success_rate = (success_count / total_tests) * 100
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✓ 稳定性测试通过")
        else:
            print("✗ 稳定性测试未通过，需要进一步优化")
        
        print("\n=" * 50)
        print("测试完成")
        print("=" * 50)
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 确保资源被清理
        await service.close()

async def main():
    """主函数"""
    try:
        success = await test_qrcode_generation()
        if success:
            print("\n所有测试通过！")
            return 0
        else:
            print("\n测试失败，请检查问题！")
            return 1
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
