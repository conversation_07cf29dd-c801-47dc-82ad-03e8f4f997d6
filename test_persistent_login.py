#!/usr/bin/env python3
"""
测试微信公众号登录状态持久化功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_service import WeChatMPService

async def test_persistent_login():
    """测试持久化登录功能"""
    print("=== 测试微信公众号登录状态持久化 ===")
    
    # 测试账号ID
    test_account_id = 1
    
    # 创建服务实例
    wechat_service = WeChatMPService(account_id=test_account_id)
    
    try:
        print(f"\n1. 尝试加载已保存的登录状态...")
        # 首先尝试加载已保存的登录状态
        if await wechat_service.load_login_state():
            print("✅ 成功加载已保存的登录状态")
            
            # 检查登录是否仍然有效
            if await wechat_service.check_existing_login():
                print("✅ 登录状态仍然有效")
                return True
            else:
                print("❌ 登录状态已失效")
        else:
            print("ℹ️  没有找到已保存的登录状态")
        
        print(f"\n2. 获取新的登录二维码...")
        # 获取二维码
        qr_result = await wechat_service.get_login_qrcode()
        
        if qr_result == "already_logged_in":
            print("✅ 检测到已经登录")
            return True
        elif qr_result and qr_result.startswith("data:image"):
            print("✅ 成功获取二维码")
            print("请使用微信扫描二维码登录...")
            
            # 等待用户扫码登录
            print(f"\n3. 等待用户扫码登录...")
            login_success = await wechat_service.wait_for_login_complete(max_wait_time=300)
            
            if login_success:
                print("✅ 登录成功！")
                print("登录状态已自动保存，下次启动时可以直接使用")
                return True
            else:
                print("❌ 登录超时或失败")
                return False
        else:
            print("❌ 获取二维码失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 清理资源（但不删除保存的登录状态）
        await wechat_service.close()

async def test_download_with_persistent_login():
    """测试使用持久化登录状态下载数据"""
    print("\n=== 测试使用持久化登录下载数据 ===")
    
    test_account_id = 1
    wechat_service = WeChatMPService(account_id=test_account_id)
    
    try:
        # 尝试加载登录状态
        if await wechat_service.load_login_state():
            print("✅ 成功加载登录状态")
            
            # 测试下载功能
            print("🔄 尝试下载数据...")
            
            # 计算日期范围（最近30天）
            from datetime import datetime, timedelta
            end_date = datetime.now() - timedelta(days=1)
            start_date = end_date - timedelta(days=29)
            
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            print(f"下载日期范围: {start_date_str} 到 {end_date_str}")
            
            excel_data = await wechat_service.download_data_excel(
                begin_date=start_date_str,
                end_date=end_date_str
            )
            
            if excel_data:
                print(f"✅ 下载成功！文件大小: {len(excel_data)} bytes")
                
                # 保存到文件用于验证
                output_file = f"test_download_{test_account_id}.xlsx"
                with open(output_file, 'wb') as f:
                    f.write(excel_data)
                print(f"📁 数据已保存到: {output_file}")
                return True
            else:
                print("❌ 下载失败")
                return False
        else:
            print("❌ 无法加载登录状态，请先完成登录")
            return False
            
    except Exception as e:
        print(f"❌ 下载测试中发生错误: {e}")
        return False
    finally:
        await wechat_service.close()

async def main():
    """主测试函数"""
    print("选择测试模式:")
    print("1. 测试登录和状态保存")
    print("2. 测试使用已保存状态下载数据")
    print("3. 运行完整测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        success = await test_persistent_login()
    elif choice == "2":
        success = await test_download_with_persistent_login()
    elif choice == "3":
        # 运行完整测试
        print("🚀 开始完整测试...")
        login_success = await test_persistent_login()
        if login_success:
            download_success = await test_download_with_persistent_login()
            success = download_success
        else:
            success = False
    else:
        print("❌ 无效选择")
        return
    
    if success:
        print("\n🎉 测试成功完成！")
    else:
        print("\n💥 测试失败")

if __name__ == "__main__":
    asyncio.run(main())
