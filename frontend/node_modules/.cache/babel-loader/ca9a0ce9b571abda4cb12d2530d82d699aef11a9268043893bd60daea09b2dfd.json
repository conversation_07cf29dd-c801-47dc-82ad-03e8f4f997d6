{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tag, Popconfirm, Image, Spin } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst AccountManage = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState(null); // 存储正在注销的账号ID\n\n  const platformOptions = [{\n    value: 'wechat_mp',\n    label: '微信公众号'\n  }, {\n    value: 'wechat_service',\n    label: '微信服务号'\n  }, {\n    value: 'xiaohongshu',\n    label: '小红书'\n  }];\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = account => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败');\n    }\n  };\n  const handleLogin = async account => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n  const startLoginStatusPolling = accountId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const {\n          logged_in\n        } = response.data;\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = account => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n\n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      busi: 3,\n      tmpl: 19\n    });\n    setDownloadModalVisible(true);\n  };\n  const handleDownloadSubmit = async values => {\n    if (!downloadAccount) return;\n    setDownloadLoading(true);\n    try {\n      var _values$busi, _values$tmpl;\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: ((_values$busi = values.busi) === null || _values$busi === void 0 ? void 0 : _values$busi.toString()) || '3',\n        tmpl: ((_values$tmpl = values.tmpl) === null || _values$tmpl === void 0 ? void 0 : _values$tmpl.toString()) || '19'\n      });\n\n      // 使用相对路径，让axios的baseURL生效\n      const downloadUrl = `http://localhost:8000/api/wechat/download-data/${downloadAccount.id}?${params}`;\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          console.log('下载URL:', downloadUrl);\n\n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          console.log('响应状态:', response.status);\n          console.log('响应头:', Object.fromEntries(response.headers.entries()));\n          if (!response.ok) {\n            const errorText = await response.text();\n            console.error('错误响应:', errorText);\n            throw new Error(`下载失败: ${response.status} - ${errorText}`);\n          }\n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n\n          // 创建下载链接\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          message.success('数据下载成功');\n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/api/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async account => {\n    try {\n      setLogoutLoading(account.id);\n      const response = await api.post(`/api/wechat/force-logout/${account.id}`);\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/api/wechat/logout-all');\n      const {\n        success,\n        message: msg,\n        logout_results\n      } = response.data;\n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n\n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter(r => r.success).length;\n        const totalCount = logout_results.length;\n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n\n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const columns = [{\n    title: '账号名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '平台类型',\n    dataIndex: 'platform',\n    key: 'platform',\n    render: platform => {\n      const option = platformOptions.find(opt => opt.value === platform);\n      return option ? option.label : platform;\n    }\n  }, {\n    title: '登录状态',\n    dataIndex: 'login_status',\n    key: 'login_status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status ? 'green' : 'red',\n      children: status ? '已登录' : '未登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后登录时间',\n    dataIndex: 'last_login_time',\n    key: 'last_login_time',\n    render: time => time ? new Date(time).toLocaleString() : '-'\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleLogin(record),\n        disabled: record.login_status,\n        children: \"\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDownload(record),\n        disabled: !record.login_status,\n        children: \"\\u4E0B\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8D26\\u53F7\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8D26\\u53F7\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 19\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u8D26\\u53F7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: accounts,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAccount ? '编辑账号' : '添加账号',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8D26\\u53F7\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入账号名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D26\\u53F7\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"platform\",\n          label: \"\\u5E73\\u53F0\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择平台类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E73\\u53F0\\u7C7B\\u578B\",\n            children: platformOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `登录 ${loginAccount === null || loginAccount === void 0 ? void 0 : loginAccount.name}`,\n      open: loginModalVisible,\n      onCancel: handleLoginModalClose,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoginModalClose,\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => loginAccount && handleLogin(loginAccount),\n        disabled: loginLoading || loginStatus === 'success',\n        children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801\"\n      }, \"retry\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 11\n      }, this)],\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px 0'\n        },\n        children: [loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16\n            },\n            children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this), loginStatus === 'scanning' && qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: qrCodeUrl,\n            alt: \"\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801\",\n            width: 200,\n            height: 200,\n            style: {\n              border: '1px solid #d9d9d9'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16,\n              color: '#1890ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this), \" \\u8BF7\\u4F7F\\u7528\\u5FAE\\u4FE1\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\\u767B\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: \"\\u4E8C\\u7EF4\\u7801\\u5C06\\u572830\\u79D2\\u540E\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this), loginStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#52c41a',\n              marginBottom: 16\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u6210\\u529F\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 13\n        }, this), loginStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#ff4d4f',\n              marginBottom: 16\n            },\n            children: \"\\u2717\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this), loginStatus === 'waiting' && !loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {\n            style: {\n              fontSize: '48px',\n              color: '#d9d9d9',\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u51C6\\u5907\\u83B7\\u53D6\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `下载数据 - ${downloadAccount === null || downloadAccount === void 0 ? void 0 : downloadAccount.name}`,\n      open: downloadModalVisible,\n      onCancel: handleDownloadModalClose,\n      onOk: () => downloadForm.submit(),\n      okText: \"\\u4E0B\\u8F7D\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: downloadLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: downloadForm,\n        layout: \"vertical\",\n        onFinish: handleDownloadSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"start_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"busi\",\n          label: \"\\u4E1A\\u52A1\\u7C7B\\u578B\",\n          initialValue: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 3,\n              children: \"\\u9ED8\\u8BA4\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tmpl\",\n          label: \"\\u6A21\\u677F\\u7C7B\\u578B\",\n          initialValue: 19,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 19,\n              children: \"\\u9ED8\\u8BA4\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u6A21\\u677F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u6A21\\u677F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u5C06\\u4E0B\\u8F7D\\u9009\\u5B9A\\u65F6\\u95F4\\u8303\\u56F4\\u5185\\u7684\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u9ED8\\u8BA4\\u9009\\u62E9\\u524D\\u4E00\\u5929\\u7ED3\\u675F\\uFF0C\\u5411\\u524D30\\u5929\\u7684\\u6570\\u636E\\u8303\\u56F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u6587\\u4EF6\\u683C\\u5F0F\\u4E3AExcel(.xlsx)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4E0B\\u8F7D\\u65F6\\u95F4\\u53EF\\u80FD\\u8F83\\u957F\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 449,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountManage, \"vCFWepyjClOyJT1sHUaS9PFmTcQ=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = AccountManage;\nexport default AccountManage;\nvar _c;\n$RefreshReg$(_c, \"AccountManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Tag", "Popconfirm", "Image", "Spin", "PlusOutlined", "EditOutlined", "DeleteOutlined", "LoginOutlined", "QrcodeOutlined", "DownloadOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "AccountManage", "_s", "accounts", "setAccounts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingAccount", "setEditingAccount", "form", "useForm", "loginModalVisible", "setLoginModalVisible", "loginAccount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "qrCodeUrl", "setQrCodeUrl", "loginLoading", "setLoginLoading", "loginStatus", "setLoginStatus", "downloadModalVisible", "setDownloadModalVisible", "downloadAccount", "setDownloadAccount", "downloadLoading", "setDownloadLoading", "downloadForm", "logoutLoading", "setLogoutLoading", "platformOptions", "value", "label", "fetchAccounts", "response", "get", "data", "error", "handleAdd", "resetFields", "handleEdit", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "delete", "success", "handleSubmit", "values", "put", "post", "_error$response", "_error$response$data", "detail", "handleLogin", "qrcode", "startLoginStatusPolling", "_error$response2", "_error$response2$data", "accountId", "pollInterval", "setInterval", "logged_in", "clearInterval", "console", "setTimeout", "warning", "handleLoginModalClose", "handleDownload", "login_status", "today", "Date", "endDate", "setDate", "getDate", "startDate", "start_date", "toISOString", "split", "end_date", "busi", "tmpl", "handleDownloadSubmit", "_values$busi", "_values$tmpl", "params", "URLSearchParams", "toString", "downloadUrl", "token", "localStorage", "getItem", "log", "fetch", "method", "headers", "status", "Object", "fromEntries", "entries", "ok", "errorText", "text", "Error", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDownloadModalClose", "handleLogout", "clear_saved_state", "name", "_error$response3", "_error$response3$data", "handleForceLogout", "_error$response4", "_error$response4$data", "handleLogoutAll", "msg", "logout_results", "length", "successCount", "filter", "r", "totalCount", "_error$response5", "_error$response5$data", "columns", "title", "dataIndex", "key", "render", "platform", "option", "find", "opt", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "time", "toLocaleString", "_", "record", "size", "type", "icon", "onClick", "disabled", "onConfirm", "okText", "cancelText", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "layout", "onFinish", "<PERSON><PERSON>", "rules", "required", "placeholder", "map", "footer", "width", "style", "textAlign", "padding", "marginTop", "src", "alt", "height", "border", "fontSize", "marginBottom", "confirmLoading", "initialValue", "background", "borderRadius", "margin", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Popconfirm,\n  Image,\n  Spin\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  platform: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n}\n\nconst AccountManage: React.FC = () => {\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState<Account | null>(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState<Account | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadForm] = Form.useForm();\n\n  // 注销相关状态\n  const [logoutLoading, setLogoutLoading] = useState<number | null>(null); // 存储正在注销的账号ID\n\n  const platformOptions = [\n    { value: 'wechat_mp', label: '微信公众号' },\n    { value: 'wechat_service', label: '微信服务号' },\n    { value: 'xiaohongshu', label: '小红书' },\n  ];\n\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (account: Account) => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleLogin = async (account: Account) => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n\n  const startLoginStatusPolling = (accountId: number) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const { logged_in } = response.data;\n\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = (account: Account) => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n    \n    // 设置默认的日期范围（前一天结束，向前30天）\n    const today = new Date();\n    const endDate = new Date(today);\n    endDate.setDate(today.getDate() - 1); // 前一天\n    \n    const startDate = new Date(endDate);\n    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天\n    \n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      busi: 3,\n      tmpl: 19\n    });\n    \n    setDownloadModalVisible(true);\n  };\n\n  const handleDownloadSubmit = async (values: any) => {\n    if (!downloadAccount) return;\n    \n    setDownloadLoading(true);\n    try {\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: values.busi?.toString() || '3',\n        tmpl: values.tmpl?.toString() || '19'\n      });\n      \n      // 使用相对路径，让axios的baseURL生效\n      const downloadUrl = `http://localhost:8000/api/wechat/download-data/${downloadAccount.id}?${params}`;\n      \n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          console.log('下载URL:', downloadUrl);\n          \n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n          \n          console.log('响应状态:', response.status);\n          console.log('响应头:', Object.fromEntries(response.headers.entries()));\n          \n          if (!response.ok) {\n            const errorText = await response.text();\n            console.error('错误响应:', errorText);\n            throw new Error(`下载失败: ${response.status} - ${errorText}`);\n          }\n          \n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n          \n          // 创建下载链接\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          \n          message.success('数据下载成功');\n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error: any) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error: any) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n\n  // 注销处理函数\n  const handleLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/api/wechat/logout/${account.id}`, {\n        clear_saved_state: true\n      });\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 注销成功`);\n        // 刷新账号列表\n        fetchAccounts();\n      } else {\n        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);\n        // 仍然刷新列表\n        fetchAccounts();\n      }\n    } catch (error: any) {\n      console.error('注销失败:', error);\n      message.error(`注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 强制注销处理函数\n  const handleForceLogout = async (account: Account) => {\n    try {\n      setLogoutLoading(account.id);\n      \n      const response = await api.post(`/api/wechat/force-logout/${account.id}`);\n\n      if (response.data.success) {\n        message.success(`账号 ${account.name} 强制注销成功`);\n      } else {\n        message.warning(`账号 ${account.name} 强制注销完成`);\n      }\n      \n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('强制注销失败:', error);\n      message.error(`强制注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLogoutLoading(null);\n    }\n  };\n\n  // 批量注销所有账号\n  const handleLogoutAll = async () => {\n    try {\n      setLoading(true);\n      \n      const response = await api.get('/api/wechat/logout-all');\n      \n      const { success, message: msg, logout_results } = response.data;\n      \n      if (success) {\n        message.success(msg);\n      } else {\n        message.warning(msg);\n      }\n      \n      // 显示详细结果\n      if (logout_results && logout_results.length > 0) {\n        const successCount = logout_results.filter((r: any) => r.success).length;\n        const totalCount = logout_results.length;\n        \n        if (successCount === totalCount) {\n          message.success(`所有 ${totalCount} 个账号注销成功`);\n        } else {\n          message.warning(`${successCount}/${totalCount} 个账号注销成功`);\n        }\n      }\n      \n      // 刷新账号列表\n      fetchAccounts();\n    } catch (error: any) {\n      console.error('批量注销失败:', error);\n      message.error(`批量注销失败: ${error.response?.data?.detail || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns = [\n    {\n      title: '账号名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '平台类型',\n      dataIndex: 'platform',\n      key: 'platform',\n      render: (platform: string) => {\n        const option = platformOptions.find(opt => opt.value === platform);\n        return option ? option.label : platform;\n      },\n    },\n    {\n      title: '登录状态',\n      dataIndex: 'login_status',\n      key: 'login_status',\n      render: (status: boolean) => (\n        <Tag color={status ? 'green' : 'red'}>\n          {status ? '已登录' : '未登录'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'last_login_time',\n      key: 'last_login_time',\n      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: Account) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"primary\" \n            icon={<LoginOutlined />} \n            size=\"small\"\n            onClick={() => handleLogin(record)}\n            disabled={record.login_status}\n          >\n            登录\n          </Button>\n          <Button \n            type=\"default\"\n            icon={<DownloadOutlined />} \n            size=\"small\"\n            onClick={() => handleDownload(record)}\n            disabled={!record.login_status}\n          >\n            下载\n          </Button>\n          <Button \n            icon={<EditOutlined />} \n            size=\"small\"\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个账号吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              danger \n              icon={<DeleteOutlined />} \n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card \n        title=\"账号管理\" \n        extra={\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={handleAdd}\n          >\n            添加账号\n          </Button>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={accounts}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAccount ? '编辑账号' : '添加账号'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"账号名称\"\n            rules={[{ required: true, message: '请输入账号名称' }]}\n          >\n            <Input placeholder=\"请输入账号名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"platform\"\n            label=\"平台类型\"\n            rules={[{ required: true, message: '请选择平台类型' }]}\n          >\n            <Select placeholder=\"请选择平台类型\">\n              {platformOptions.map(option => (\n                <Option key={option.value} value={option.value}>\n                  {option.label}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 登录模态框 */}\n      <Modal\n        title={`登录 ${loginAccount?.name}`}\n        open={loginModalVisible}\n        onCancel={handleLoginModalClose}\n        footer={[\n          <Button key=\"close\" onClick={handleLoginModalClose}>\n            关闭\n          </Button>,\n          <Button\n            key=\"retry\"\n            type=\"primary\"\n            onClick={() => loginAccount && handleLogin(loginAccount)}\n            disabled={loginLoading || loginStatus === 'success'}\n          >\n            重新获取二维码\n          </Button>\n        ]}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          {loginLoading && (\n            <div>\n              <Spin size=\"large\" />\n              <p style={{ marginTop: 16 }}>正在获取二维码...</p>\n            </div>\n          )}\n\n          {loginStatus === 'scanning' && qrCodeUrl && (\n            <div>\n              <Image\n                src={qrCodeUrl}\n                alt=\"登录二维码\"\n                width={200}\n                height={200}\n                style={{ border: '1px solid #d9d9d9' }}\n              />\n              <p style={{ marginTop: 16, color: '#1890ff' }}>\n                <QrcodeOutlined /> 请使用微信扫描二维码登录\n              </p>\n              <p style={{ color: '#666', fontSize: '12px' }}>\n                二维码将在30秒后过期\n              </p>\n            </div>\n          )}\n\n          {loginStatus === 'success' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>\n                ✓\n              </div>\n              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>\n            </div>\n          )}\n\n          {loginStatus === 'failed' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>\n                ✗\n              </div>\n              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>\n            </div>\n          )}\n\n          {loginStatus === 'waiting' && !loginLoading && (\n            <div>\n              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />\n              <p style={{ color: '#666' }}>准备获取登录二维码...</p>\n            </div>\n          )}\n        </div>\n      </Modal>\n\n      {/* 下载数据模态框 */}\n      <Modal\n        title={`下载数据 - ${downloadAccount?.name}`}\n        open={downloadModalVisible}\n        onCancel={handleDownloadModalClose}\n        onOk={() => downloadForm.submit()}\n        okText=\"下载\"\n        cancelText=\"取消\"\n        confirmLoading={downloadLoading}\n        width={500}\n      >\n        <Form\n          form={downloadForm}\n          layout=\"vertical\"\n          onFinish={handleDownloadSubmit}\n        >\n          <Form.Item\n            name=\"start_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"busi\"\n            label=\"业务类型\"\n            initialValue={3}\n          >\n            <Select>\n              <Option value={3}>默认业务</Option>\n              <Option value={1}>其他业务1</Option>\n              <Option value={2}>其他业务2</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"tmpl\"\n            label=\"模板类型\"\n            initialValue={19}\n          >\n            <Select>\n              <Option value={19}>默认模板</Option>\n              <Option value={1}>模板1</Option>\n              <Option value={2}>模板2</Option>\n            </Select>\n          </Form.Item>\n          \n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>将下载选定时间范围内的微信公众号数据</li>\n              <li>默认选择前一天结束，向前30天的数据范围</li>\n              <li>文件格式为Excel(.xlsx)</li>\n              <li>请确保账号已登录状态</li>\n              <li>下载时间可能较长，请耐心等待</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AccountManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,QACC,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,QAAwB,mBAAmB;AAC/I,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAO,CAAC,GAAGhB,MAAM;AAWzB,MAAMiB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACmC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAiB,IAAI,CAAC;EACtE,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAS,SAAS,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC+C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,YAAY,CAAC,GAAG/C,IAAI,CAAC8B,OAAO,CAAC,CAAC;;EAErC;EACA,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;;EAEzE,MAAMwD,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAM,CAAC,CACvC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAMvC,GAAG,CAACwC,GAAG,CAAC,YAAY,CAAC;MAC5CjC,WAAW,CAACgC,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED7B,SAAS,CAAC,MAAM;IACd0D,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB9B,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAAC8B,WAAW,CAAC,CAAC;IAClBjC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkC,UAAU,GAAIC,OAAgB,IAAK;IACvCjC,iBAAiB,CAACiC,OAAO,CAAC;IAC1BhC,IAAI,CAACiC,cAAc,CAACD,OAAO,CAAC;IAC5BnC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqC,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMjD,GAAG,CAACkD,MAAM,CAAC,aAAaD,EAAE,EAAE,CAAC;MACnC7D,OAAO,CAAC+D,OAAO,CAAC,MAAM,CAAC;MACvBb,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAIzC,cAAc,EAAE;QAClB,MAAMZ,GAAG,CAACsD,GAAG,CAAC,aAAa1C,cAAc,CAACqC,EAAE,EAAE,EAAEI,MAAM,CAAC;QACvDjE,OAAO,CAAC+D,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMnD,GAAG,CAACuD,IAAI,CAAC,YAAY,EAAEF,MAAM,CAAC;QACpCjE,OAAO,CAAC+D,OAAO,CAAC,MAAM,CAAC;MACzB;MACAxC,eAAe,CAAC,KAAK,CAAC;MACtB2B,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnBrE,OAAO,CAACsD,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOb,OAAgB,IAAK;IAC9C3B,eAAe,CAAC2B,OAAO,CAAC;IACxB7B,oBAAoB,CAAC,IAAI,CAAC;IAC1BQ,cAAc,CAAC,SAAS,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMgB,QAAQ,GAAG,MAAMvC,GAAG,CAACuD,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MACrE,IAAIV,QAAQ,CAACE,IAAI,CAACmB,MAAM,EAAE;QACxBvC,YAAY,CAACkB,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC;QAClCnC,cAAc,CAAC,UAAU,CAAC;;QAE1B;QACAoC,uBAAuB,CAACf,OAAO,CAACG,EAAE,CAAC;MACrC,CAAC,MAAM;QACL7D,OAAO,CAACsD,KAAK,CAAC,SAAS,CAAC;QACxBjB,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiB,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB3E,OAAO,CAACsD,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,SAAS,CAAC;MACxDjC,cAAc,CAAC,QAAQ,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsC,uBAAuB,GAAIG,SAAiB,IAAK;IACrD,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMvC,GAAG,CAACwC,GAAG,CAAC,wBAAwBwB,SAAS,EAAE,CAAC;QACnE,MAAM;UAAEG;QAAU,CAAC,GAAG5B,QAAQ,CAACE,IAAI;QAEnC,IAAI0B,SAAS,EAAE;UACb1C,cAAc,CAAC,SAAS,CAAC;UACzBrC,OAAO,CAAC+D,OAAO,CAAC,OAAO,CAAC;UACxBiB,aAAa,CAACH,YAAY,CAAC;UAC3BhD,oBAAoB,CAAC,KAAK,CAAC;UAC3BqB,aAAa,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA4B,UAAU,CAAC,MAAM;MACfF,aAAa,CAACH,YAAY,CAAC;MAC3B,IAAIzC,WAAW,KAAK,UAAU,EAAE;QAC9BC,cAAc,CAAC,QAAQ,CAAC;QACxBrC,OAAO,CAACmF,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClCvD,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMgD,cAAc,GAAI3B,OAAgB,IAAK;IAC3C,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;MACzBtF,OAAO,CAACmF,OAAO,CAAC,cAAc,CAAC;MAC/B;IACF;IACA1C,kBAAkB,CAACiB,OAAO,CAAC;;IAE3B;IACA,MAAM6B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAC/BE,OAAO,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEtC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACC,OAAO,CAAC;IACnCG,SAAS,CAACF,OAAO,CAACD,OAAO,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;IAE3C/C,YAAY,CAACe,cAAc,CAAC;MAC1BkC,UAAU,EAAED,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAEP,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CE,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,CAAC;IAEF3D,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAM4D,oBAAoB,GAAG,MAAOlC,MAAW,IAAK;IAClD,IAAI,CAACzB,eAAe,EAAE;IAEtBG,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MAAA,IAAAyD,YAAA,EAAAC,YAAA;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCV,UAAU,EAAE5B,MAAM,CAAC4B,UAAU;QAC7BG,QAAQ,EAAE/B,MAAM,CAAC+B,QAAQ;QACzBC,IAAI,EAAE,EAAAG,YAAA,GAAAnC,MAAM,CAACgC,IAAI,cAAAG,YAAA,uBAAXA,YAAA,CAAaI,QAAQ,CAAC,CAAC,KAAI,GAAG;QACpCN,IAAI,EAAE,EAAAG,YAAA,GAAApC,MAAM,CAACiC,IAAI,cAAAG,YAAA,uBAAXA,YAAA,CAAaG,QAAQ,CAAC,CAAC,KAAI;MACnC,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAW,GAAG,kDAAkDjE,eAAe,CAACqB,EAAE,IAAIyC,MAAM,EAAE;MAEpG,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACFzB,OAAO,CAAC4B,GAAG,CAAC,QAAQ,EAAEJ,WAAW,CAAC;;UAElC;UACA,MAAMtD,QAAQ,GAAG,MAAM2D,KAAK,CAACL,WAAW,EAAE;YACxCM,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUN,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UAEFzB,OAAO,CAAC4B,GAAG,CAAC,OAAO,EAAE1D,QAAQ,CAAC8D,MAAM,CAAC;UACrChC,OAAO,CAAC4B,GAAG,CAAC,MAAM,EAAEK,MAAM,CAACC,WAAW,CAAChE,QAAQ,CAAC6D,OAAO,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;UAEnE,IAAI,CAACjE,QAAQ,CAACkE,EAAE,EAAE;YAChB,MAAMC,SAAS,GAAG,MAAMnE,QAAQ,CAACoE,IAAI,CAAC,CAAC;YACvCtC,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEgE,SAAS,CAAC;YACjC,MAAM,IAAIE,KAAK,CAAC,SAASrE,QAAQ,CAAC8D,MAAM,MAAMK,SAAS,EAAE,CAAC;UAC5D;UAEA,MAAMG,IAAI,GAAG,MAAMtE,QAAQ,CAACsE,IAAI,CAAC,CAAC;UAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;UAE5C;UACA,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,eAAejE,MAAM,CAAC4B,UAAU,OAAO5B,MAAM,CAAC+B,QAAQ,OAAO;UAC7E+B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;UAE/B1H,OAAO,CAAC+D,OAAO,CAAC,QAAQ,CAAC;UACzB/D,OAAO,CAAC+D,OAAO,CAAC,QAAQ,CAAC;UACzBxB,uBAAuB,CAAC,KAAK,CAAC;QAChC,CAAC,CAAC,OAAOe,KAAU,EAAE;UACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;UAC7BtD,OAAO,CAACsD,KAAK,CAAC,SAASA,KAAK,CAACtD,OAAO,EAAE,CAAC;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,CAACsD,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BtD,OAAO,CAACsD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRX,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAM6F,wBAAwB,GAAGA,CAAA,KAAM;IACrCjG,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;IACxBG,YAAY,CAACY,WAAW,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMiF,YAAY,GAAG,MAAO/E,OAAgB,IAAK;IAC/C,IAAI;MACFZ,gBAAgB,CAACY,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMvC,GAAG,CAACuD,IAAI,CAAC,sBAAsBT,OAAO,CAACG,EAAE,EAAE,EAAE;QAClE6E,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIvF,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB/D,OAAO,CAAC+D,OAAO,CAAC,MAAML,OAAO,CAACiF,IAAI,OAAO,CAAC;QAC1C;QACAzF,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLlD,OAAO,CAACmF,OAAO,CAAC,MAAMzB,OAAO,CAACiF,IAAI,iBAAiB,CAAC;QACpD;QACAzF,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAsF,gBAAA,EAAAC,qBAAA;MACnB5D,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BtD,OAAO,CAACsD,KAAK,CAAC,SAAS,EAAAsF,gBAAA,GAAAtF,KAAK,CAACH,QAAQ,cAAAyF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvF,IAAI,cAAAwF,qBAAA,uBAApBA,qBAAA,CAAsBvE,MAAM,KAAI,MAAM,EAAE,CAAC;IAClE,CAAC,SAAS;MACRxB,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgG,iBAAiB,GAAG,MAAOpF,OAAgB,IAAK;IACpD,IAAI;MACFZ,gBAAgB,CAACY,OAAO,CAACG,EAAE,CAAC;MAE5B,MAAMV,QAAQ,GAAG,MAAMvC,GAAG,CAACuD,IAAI,CAAC,4BAA4BT,OAAO,CAACG,EAAE,EAAE,CAAC;MAEzE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB/D,OAAO,CAAC+D,OAAO,CAAC,MAAML,OAAO,CAACiF,IAAI,SAAS,CAAC;MAC9C,CAAC,MAAM;QACL3I,OAAO,CAACmF,OAAO,CAAC,MAAMzB,OAAO,CAACiF,IAAI,SAAS,CAAC;MAC9C;;MAEA;MACAzF,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAyF,gBAAA,EAAAC,qBAAA;MACnB/D,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtD,OAAO,CAACsD,KAAK,CAAC,WAAW,EAAAyF,gBAAA,GAAAzF,KAAK,CAACH,QAAQ,cAAA4F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1F,IAAI,cAAA2F,qBAAA,uBAApBA,qBAAA,CAAsB1E,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRxB,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMmG,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF5H,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM8B,QAAQ,GAAG,MAAMvC,GAAG,CAACwC,GAAG,CAAC,wBAAwB,CAAC;MAExD,MAAM;QAAEW,OAAO;QAAE/D,OAAO,EAAEkJ,GAAG;QAAEC;MAAe,CAAC,GAAGhG,QAAQ,CAACE,IAAI;MAE/D,IAAIU,OAAO,EAAE;QACX/D,OAAO,CAAC+D,OAAO,CAACmF,GAAG,CAAC;MACtB,CAAC,MAAM;QACLlJ,OAAO,CAACmF,OAAO,CAAC+D,GAAG,CAAC;MACtB;;MAEA;MACA,IAAIC,cAAc,IAAIA,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAMC,YAAY,GAAGF,cAAc,CAACG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACxF,OAAO,CAAC,CAACqF,MAAM;QACxE,MAAMI,UAAU,GAAGL,cAAc,CAACC,MAAM;QAExC,IAAIC,YAAY,KAAKG,UAAU,EAAE;UAC/BxJ,OAAO,CAAC+D,OAAO,CAAC,MAAMyF,UAAU,UAAU,CAAC;QAC7C,CAAC,MAAM;UACLxJ,OAAO,CAACmF,OAAO,CAAC,GAAGkE,YAAY,IAAIG,UAAU,UAAU,CAAC;QAC1D;MACF;;MAEA;MACAtG,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAmG,gBAAA,EAAAC,qBAAA;MACnBzE,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtD,OAAO,CAACsD,KAAK,CAAC,WAAW,EAAAmG,gBAAA,GAAAnG,KAAK,CAACH,QAAQ,cAAAsG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpG,IAAI,cAAAqG,qBAAA,uBAApBA,qBAAA,CAAsBpF,MAAM,KAAI,MAAM,EAAE,CAAC;IACpE,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsI,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,QAAgB,IAAK;MAC5B,MAAMC,MAAM,GAAGlH,eAAe,CAACmH,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnH,KAAK,KAAKgH,QAAQ,CAAC;MAClE,OAAOC,MAAM,GAAGA,MAAM,CAAChH,KAAK,GAAG+G,QAAQ;IACzC;EACF,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAG9C,MAAe,iBACtBnG,OAAA,CAACZ,GAAG;MAACkK,KAAK,EAAEnD,MAAM,GAAG,OAAO,GAAG,KAAM;MAAAoD,QAAA,EAClCpD,MAAM,GAAG,KAAK,GAAG;IAAK;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAGW,IAAmB,IAAKA,IAAI,GAAG,IAAIlF,IAAI,CAACkF,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC5E,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGW,IAAY,IAAK,IAAIlF,IAAI,CAACkF,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACa,CAAM,EAAEC,MAAe,kBAC9B/J,OAAA,CAACb,KAAK;MAAC6K,IAAI,EAAC,QAAQ;MAAAT,QAAA,gBAClBvJ,OAAA,CAACnB,MAAM;QACLoL,IAAI,EAAC,SAAS;QACdC,IAAI,eAAElK,OAAA,CAACL,aAAa;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM1G,WAAW,CAACsG,MAAM,CAAE;QACnCK,QAAQ,EAAEL,MAAM,CAACvF,YAAa;QAAA+E,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3J,OAAA,CAACnB,MAAM;QACLoL,IAAI,EAAC,SAAS;QACdC,IAAI,eAAElK,OAAA,CAACH,gBAAgB;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAACwF,MAAM,CAAE;QACtCK,QAAQ,EAAE,CAACL,MAAM,CAACvF,YAAa;QAAA+E,QAAA,EAChC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3J,OAAA,CAACnB,MAAM;QACLqL,IAAI,eAAElK,OAAA,CAACP,YAAY;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMxH,UAAU,CAACoH,MAAM,CAAE;QAAAR,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3J,OAAA,CAACX,UAAU;QACTyJ,KAAK,EAAC,oEAAa;QACnBuB,SAAS,EAAEA,CAAA,KAAMvH,YAAY,CAACiH,MAAM,CAAChH,EAAE,CAAE;QACzCuH,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhB,QAAA,eAEfvJ,OAAA,CAACnB,MAAM;UACL2L,MAAM;UACNN,IAAI,eAAElK,OAAA,CAACN,cAAc;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBK,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE3J,OAAA;IAAAuJ,QAAA,gBACEvJ,OAAA,CAACrB,IAAI;MACHmK,KAAK,EAAC,0BAAM;MACZ2B,KAAK,eACHzK,OAAA,CAACnB,MAAM;QACLoL,IAAI,EAAC,SAAS;QACdC,IAAI,eAAElK,OAAA,CAACR,YAAY;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAE1H,SAAU;QAAA8G,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAJ,QAAA,eAEDvJ,OAAA,CAACpB,KAAK;QACJiK,OAAO,EAAEA,OAAQ;QACjB6B,UAAU,EAAEtK,QAAS;QACrBuK,MAAM,EAAC,IAAI;QACXrK,OAAO,EAAEA,OAAQ;QACjBsK,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP3J,OAAA,CAAClB,KAAK;MACJgK,KAAK,EAAEpI,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCuK,IAAI,EAAEzK,YAAa;MACnB0K,QAAQ,EAAEA,CAAA,KAAMzK,eAAe,CAAC,KAAK,CAAE;MACvC0K,IAAI,EAAEA,CAAA,KAAMvK,IAAI,CAACwK,MAAM,CAAC,CAAE;MAC1Bd,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAhB,QAAA,eAEfvJ,OAAA,CAACjB,IAAI;QACH6B,IAAI,EAAEA,IAAK;QACXyK,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEpI,YAAa;QAAAqG,QAAA,gBAEvBvJ,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX1F,KAAK,EAAC,0BAAM;UACZqJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDvJ,OAAA,CAAChB,KAAK;YAAC0M,WAAW,EAAC;UAAS;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZ3J,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,UAAU;UACf1F,KAAK,EAAC,0BAAM;UACZqJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDvJ,OAAA,CAACf,MAAM;YAACyM,WAAW,EAAC,4CAAS;YAAAnC,QAAA,EAC1BtH,eAAe,CAAC0J,GAAG,CAACxC,MAAM,iBACzBnJ,OAAA,CAACC,MAAM;cAAoBiC,KAAK,EAAEiH,MAAM,CAACjH,KAAM;cAAAqH,QAAA,EAC5CJ,MAAM,CAAChH;YAAK,GADFgH,MAAM,CAACjH,KAAK;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR3J,OAAA,CAAClB,KAAK;MACJgK,KAAK,EAAE,MAAM9H,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6G,IAAI,EAAG;MAClCoD,IAAI,EAAEnK,iBAAkB;MACxBoK,QAAQ,EAAE5G,qBAAsB;MAChCsH,MAAM,EAAE,cACN5L,OAAA,CAACnB,MAAM;QAAasL,OAAO,EAAE7F,qBAAsB;QAAAiF,QAAA,EAAC;MAEpD,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT3J,OAAA,CAACnB,MAAM;QAELoL,IAAI,EAAC,SAAS;QACdE,OAAO,EAAEA,CAAA,KAAMnJ,YAAY,IAAIyC,WAAW,CAACzC,YAAY,CAAE;QACzDoJ,QAAQ,EAAEhJ,YAAY,IAAIE,WAAW,KAAK,SAAU;QAAAiI,QAAA,EACrD;MAED,GANM,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CAAC,CACT;MACFkC,KAAK,EAAE,GAAI;MAAAtC,QAAA,eAEXvJ,OAAA;QAAK8L,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAzC,QAAA,GACpDnI,YAAY,iBACXpB,OAAA;UAAAuJ,QAAA,gBACEvJ,OAAA,CAACT,IAAI;YAACyK,IAAI,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrB3J,OAAA;YAAG8L,KAAK,EAAE;cAAEG,SAAS,EAAE;YAAG,CAAE;YAAA1C,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,EAEArI,WAAW,KAAK,UAAU,IAAIJ,SAAS,iBACtClB,OAAA;UAAAuJ,QAAA,gBACEvJ,OAAA,CAACV,KAAK;YACJ4M,GAAG,EAAEhL,SAAU;YACfiL,GAAG,EAAC,gCAAO;YACXN,KAAK,EAAE,GAAI;YACXO,MAAM,EAAE,GAAI;YACZN,KAAK,EAAE;cAAEO,MAAM,EAAE;YAAoB;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACF3J,OAAA;YAAG8L,KAAK,EAAE;cAAEG,SAAS,EAAE,EAAE;cAAE3C,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,gBAC5CvJ,OAAA,CAACJ,cAAc;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3J,OAAA;YAAG8L,KAAK,EAAE;cAAExC,KAAK,EAAE,MAAM;cAAEgD,QAAQ,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEArI,WAAW,KAAK,SAAS,iBACxBtB,OAAA;UAAAuJ,QAAA,gBACEvJ,OAAA;YAAK8L,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEhD,KAAK,EAAE,SAAS;cAAEiD,YAAY,EAAE;YAAG,CAAE;YAAAhD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3J,OAAA;YAAG8L,KAAK,EAAE;cAAExC,KAAK,EAAE,SAAS;cAAEgD,QAAQ,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEArI,WAAW,KAAK,QAAQ,iBACvBtB,OAAA;UAAAuJ,QAAA,gBACEvJ,OAAA;YAAK8L,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEhD,KAAK,EAAE,SAAS;cAAEiD,YAAY,EAAE;YAAG,CAAE;YAAAhD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3J,OAAA;YAAG8L,KAAK,EAAE;cAAExC,KAAK,EAAE,SAAS;cAAEgD,QAAQ,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEArI,WAAW,KAAK,SAAS,IAAI,CAACF,YAAY,iBACzCpB,OAAA;UAAAuJ,QAAA,gBACEvJ,OAAA,CAACJ,cAAc;YAACkM,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEhD,KAAK,EAAE,SAAS;cAAEiD,YAAY,EAAE;YAAG;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnF3J,OAAA;YAAG8L,KAAK,EAAE;cAAExC,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR3J,OAAA,CAAClB,KAAK;MACJgK,KAAK,EAAE,UAAUpH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmG,IAAI,EAAG;MACzCoD,IAAI,EAAEzJ,oBAAqB;MAC3B0J,QAAQ,EAAExD,wBAAyB;MACnCyD,IAAI,EAAEA,CAAA,KAAMrJ,YAAY,CAACsJ,MAAM,CAAC,CAAE;MAClCd,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfiC,cAAc,EAAE5K,eAAgB;MAChCiK,KAAK,EAAE,GAAI;MAAAtC,QAAA,eAEXvJ,OAAA,CAACjB,IAAI;QACH6B,IAAI,EAAEkB,YAAa;QACnBuJ,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEjG,oBAAqB;QAAAkE,QAAA,gBAE/BvJ,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,YAAY;UACjB1F,KAAK,EAAC,0BAAM;UACZqJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDvJ,OAAA,CAAChB,KAAK;YAACiL,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZ3J,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,UAAU;UACf1F,KAAK,EAAC,0BAAM;UACZqJ,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqK,QAAA,eAEhDvJ,OAAA,CAAChB,KAAK;YAACiL,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC;UAAS;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZ3J,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX1F,KAAK,EAAC,0BAAM;UACZsK,YAAY,EAAE,CAAE;UAAAlD,QAAA,eAEhBvJ,OAAA,CAACf,MAAM;YAAAsK,QAAA,gBACLvJ,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,CAAE;cAAAqH,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/B3J,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,CAAE;cAAAqH,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3J,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,CAAE;cAAAqH,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ3J,OAAA,CAACjB,IAAI,CAACwM,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX1F,KAAK,EAAC,0BAAM;UACZsK,YAAY,EAAE,EAAG;UAAAlD,QAAA,eAEjBvJ,OAAA,CAACf,MAAM;YAAAsK,QAAA,gBACLvJ,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,EAAG;cAAAqH,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3J,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,CAAE;cAAAqH,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9B3J,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAE,CAAE;cAAAqH,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ3J,OAAA;UAAK8L,KAAK,EAAE;YAAEY,UAAU,EAAE,SAAS;YAAEV,OAAO,EAAE,MAAM;YAAEW,YAAY,EAAE,KAAK;YAAEV,SAAS,EAAE;UAAO,CAAE;UAAA1C,QAAA,gBAC7FvJ,OAAA;YAAG8L,KAAK,EAAE;cAAEc,MAAM,EAAE,CAAC;cAAEN,QAAQ,EAAE,MAAM;cAAEhD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,eACvDvJ,OAAA;cAAAuJ,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJ3J,OAAA;YAAI8L,KAAK,EAAE;cAAEc,MAAM,EAAE,WAAW;cAAEC,WAAW,EAAE,MAAM;cAAEP,QAAQ,EAAE,MAAM;cAAEhD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACvFvJ,OAAA;cAAAuJ,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3B3J,OAAA;cAAAuJ,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B3J,OAAA;cAAAuJ,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B3J,OAAA;cAAAuJ,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3J,OAAA;cAAAuJ,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxJ,EAAA,CAjnBID,aAAuB;EAAA,QAKZnB,IAAI,CAAC8B,OAAO,EAaJ9B,IAAI,CAAC8B,OAAO;AAAA;AAAAiM,EAAA,GAlB/B5M,aAAuB;AAmnB7B,eAAeA,aAAa;AAAC,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}