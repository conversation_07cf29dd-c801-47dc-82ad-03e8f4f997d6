{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    login,\n    user\n  } = useAuth();\n  const [form] = Form.useForm();\n  useEffect(() => {\n    if (user) {\n      navigate('/');\n    }\n  }, [user, navigate]);\n  const onFinish = async values => {\n    const success = await login(values.username, values.password);\n    if (success) {\n      navigate('/');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      minHeight: '100vh',\n      background: '#f0f2f5'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: 400\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          children: \"\\u793E\\u4EA4\\u5A92\\u4F53\\u6570\\u636E\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        name: \"login\",\n        onFinish: onFinish,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          rules: [{\n            required: true,\n            message: '请输入用户名!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: '请输入密码!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            size: \"large\",\n            block: true,\n            children: \"\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"+Z3sVW1SNjUhfJ3lv5u34LXnZzQ=\", false, function () {\n  return [useNavigate, useAuth, Form.useForm];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useEffect", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "UserOutlined", "LockOutlined", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Title", "<PERSON><PERSON>", "_s", "navigate", "login", "user", "form", "useForm", "onFinish", "values", "success", "username", "password", "style", "display", "justifyContent", "alignItems", "minHeight", "background", "children", "width", "textAlign", "marginBottom", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "autoComplete", "<PERSON><PERSON>", "rules", "required", "message", "prefix", "placeholder", "size", "Password", "type", "htmlType", "block", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst { Title } = Typography;\n\nconst Login: React.FC = () => {\n  const navigate = useNavigate();\n  const { login, user } = useAuth();\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/');\n    }\n  }, [user, navigate]);\n\n  const onFinish = async (values: { username: string; password: string }) => {\n    const success = await login(values.username, values.password);\n    if (success) {\n      navigate('/');\n    }\n  };\n\n  return (\n    <div style={{ \n      display: 'flex', \n      justifyContent: 'center', \n      alignItems: 'center', \n      minHeight: '100vh',\n      background: '#f0f2f5'\n    }}>\n      <Card style={{ width: 400 }}>\n        <div style={{ textAlign: 'center', marginBottom: 24 }}>\n          <Title level={3}>社交媒体数据管理系统</Title>\n        </div>\n        \n        <Form\n          form={form}\n          name=\"login\"\n          onFinish={onFinish}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[{ required: true, message: '请输入用户名!' }]}\n          >\n            <Input \n              prefix={<UserOutlined />} \n              placeholder=\"用户名\" \n              size=\"large\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[{ required: true, message: '请输入密码!' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n              size=\"large\"\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" size=\"large\" block>\n              登录\n            </Button>\n          </Form.Item>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,QAAQ,MAAM;AAC5D,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAM;EAAEC;AAAM,CAAC,GAAGP,UAAU;AAE5B,MAAMQ,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ,KAAK;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EACjC,MAAM,CAACS,IAAI,CAAC,GAAGjB,IAAI,CAACkB,OAAO,CAAC,CAAC;EAE7BnB,SAAS,CAAC,MAAM;IACd,IAAIiB,IAAI,EAAE;MACRF,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACE,IAAI,EAAEF,QAAQ,CAAC,CAAC;EAEpB,MAAMK,QAAQ,GAAG,MAAOC,MAA8C,IAAK;IACzE,MAAMC,OAAO,GAAG,MAAMN,KAAK,CAACK,MAAM,CAACE,QAAQ,EAAEF,MAAM,CAACG,QAAQ,CAAC;IAC7D,IAAIF,OAAO,EAAE;MACXP,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKc,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eACApB,OAAA,CAACP,IAAI;MAACqB,KAAK,EAAE;QAAEO,KAAK,EAAE;MAAI,CAAE;MAAAD,QAAA,gBAC1BpB,OAAA;QAAKc,KAAK,EAAE;UAAEQ,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,eACpDpB,OAAA,CAACC,KAAK;UAACuB,KAAK,EAAE,CAAE;UAAAJ,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAEN5B,OAAA,CAACV,IAAI;QACHiB,IAAI,EAAEA,IAAK;QACXsB,IAAI,EAAC,OAAO;QACZpB,QAAQ,EAAEA,QAAS;QACnBqB,YAAY,EAAC,KAAK;QAAAV,QAAA,gBAElBpB,OAAA,CAACV,IAAI,CAACyC,IAAI;UACRF,IAAI,EAAC,UAAU;UACfG,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAd,QAAA,eAEhDpB,OAAA,CAACT,KAAK;YACJ4C,MAAM,eAAEnC,OAAA,CAACL,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC,oBAAK;YACjBC,IAAI,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ5B,OAAA,CAACV,IAAI,CAACyC,IAAI;UACRF,IAAI,EAAC,UAAU;UACfG,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAd,QAAA,eAE/CpB,OAAA,CAACT,KAAK,CAAC+C,QAAQ;YACbH,MAAM,eAAEnC,OAAA,CAACJ,YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC,cAAI;YAChBC,IAAI,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ5B,OAAA,CAACV,IAAI,CAACyC,IAAI;UAAAX,QAAA,eACRpB,OAAA,CAACR,MAAM;YAAC+C,IAAI,EAAC,SAAS;YAACC,QAAQ,EAAC,QAAQ;YAACH,IAAI,EAAC,OAAO;YAACI,KAAK;YAAArB,QAAA,EAAC;UAE5D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzB,EAAA,CApEID,KAAe;EAAA,QACFL,WAAW,EACJC,OAAO,EAChBR,IAAI,CAACkB,OAAO;AAAA;AAAAkC,EAAA,GAHvBxC,KAAe;AAsErB,eAAeA,KAAK;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}