{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { findListDiffIndex } from \"../utils/algorithmUtil\";\nexport default function useDiffItem(data, getKey, onDiff) {\n  var _React$useState = React.useState(data),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevData = _React$useState2[0],\n    setPrevData = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    diffItem = _React$useState4[0],\n    setDiffItem = _React$useState4[1];\n  React.useEffect(function () {\n    var diff = findListDiffIndex(prevData || [], data || [], getKey);\n    if ((diff === null || diff === void 0 ? void 0 : diff.index) !== undefined) {\n      onDiff === null || onDiff === void 0 || onDiff(diff.index);\n      setDiffItem(data[diff.index]);\n    }\n    setPrevData(data);\n  }, [data]);\n  return [diffItem];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "findListDiffIndex", "useDiffItem", "data", "<PERSON><PERSON><PERSON>", "onDiff", "_React$useState", "useState", "_React$useState2", "prevData", "setPrevData", "_React$useState3", "_React$useState4", "diffItem", "setDiffItem", "useEffect", "diff", "index", "undefined"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/node_modules/rc-virtual-list/es/hooks/useDiffItem.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { findListDiffIndex } from \"../utils/algorithmUtil\";\nexport default function useDiffItem(data, getKey, onDiff) {\n  var _React$useState = React.useState(data),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevData = _React$useState2[0],\n    setPrevData = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    diffItem = _React$useState4[0],\n    setDiffItem = _React$useState4[1];\n  React.useEffect(function () {\n    var diff = findListDiffIndex(prevData || [], data || [], getKey);\n    if ((diff === null || diff === void 0 ? void 0 : diff.index) !== undefined) {\n      onDiff === null || onDiff === void 0 || onDiff(diff.index);\n      setDiffItem(data[diff.index]);\n    }\n    setPrevData(data);\n  }, [data]);\n  return [diffItem];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACxD,IAAIC,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAACJ,IAAI,CAAC;IACxCK,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,gBAAgB,GAAGX,KAAK,CAACO,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAGb,cAAc,CAACY,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnCZ,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,IAAIC,IAAI,GAAGf,iBAAiB,CAACQ,QAAQ,IAAI,EAAE,EAAEN,IAAI,IAAI,EAAE,EAAEC,MAAM,CAAC;IAChE,IAAI,CAACY,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACC,KAAK,MAAMC,SAAS,EAAE;MAC1Eb,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACW,IAAI,CAACC,KAAK,CAAC;MAC1DH,WAAW,CAACX,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,CAAC;IAC/B;IACAP,WAAW,CAACP,IAAI,CAAC;EACnB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,OAAO,CAACU,QAAQ,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}