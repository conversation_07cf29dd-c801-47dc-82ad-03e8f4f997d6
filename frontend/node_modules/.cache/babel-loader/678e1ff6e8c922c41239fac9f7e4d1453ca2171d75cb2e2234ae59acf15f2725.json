{"ast": null, "code": "import { format } from \"../util\";\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, source, errors, options) {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')));\n  }\n};\nexport default enumerable;", "map": {"version": 3, "names": ["format", "ENUM", "enumerable", "rule", "value", "source", "errors", "options", "Array", "isArray", "indexOf", "push", "messages", "fullField", "join"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/node_modules/@rc-component/async-validator/es/rule/enum.js"], "sourcesContent": ["import { format } from \"../util\";\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, source, errors, options) {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')));\n  }\n};\nexport default enumerable;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACzEJ,IAAI,CAACF,IAAI,CAAC,GAAGO,KAAK,CAACC,OAAO,CAACN,IAAI,CAACF,IAAI,CAAC,CAAC,GAAGE,IAAI,CAACF,IAAI,CAAC,GAAG,EAAE;EACxD,IAAIE,IAAI,CAACF,IAAI,CAAC,CAACS,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCE,MAAM,CAACK,IAAI,CAACX,MAAM,CAACO,OAAO,CAACK,QAAQ,CAACX,IAAI,CAAC,EAAEE,IAAI,CAACU,SAAS,EAAEV,IAAI,CAACF,IAAI,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACpF;AACF,CAAC;AACD,eAAeZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}