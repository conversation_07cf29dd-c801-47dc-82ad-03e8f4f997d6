{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { message } from 'antd';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    checkAuth();\n  }, []);\n  const checkAuth = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const response = await api.get('/auth/me');\n        setUser(response.data);\n      }\n    } catch (error) {\n      localStorage.removeItem('token');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (username, password) => {\n    try {\n      const response = await api.post('/auth/login', {\n        username,\n        password\n      });\n      const {\n        access_token,\n        user: userData\n      } = response.data;\n      localStorage.setItem('token', access_token);\n      setUser(userData);\n      message.success('登录成功');\n      return true;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '登录失败');\n      return false;\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    message.success('已退出登录');\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      user,\n      login,\n      logout,\n      loading\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "message", "api", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "checkAuth", "token", "localStorage", "getItem", "response", "get", "data", "error", "removeItem", "login", "username", "password", "post", "access_token", "userData", "setItem", "success", "_error$response", "_error$response$data", "detail", "logout", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { message } from 'antd';\nimport api from '../services/api';\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  login: (username: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const response = await api.get('/auth/me');\n        setUser(response.data);\n      }\n    } catch (error) {\n      localStorage.removeItem('token');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (username: string, password: string): Promise<boolean> => {\n    try {\n      const response = await api.post('/auth/login', { username, password });\n      const { access_token, user: userData } = response.data;\n      \n      localStorage.setItem('token', access_token);\n      setUser(userData);\n      message.success('登录成功');\n      return true;\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '登录失败');\n      return false;\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    message.success('已退出登录');\n  };\n\n  return (\n    <AuthContext.Provider value={{ user, login, logout, loading }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAelC,MAAMC,WAAW,gBAAGR,aAAa,CAA8BS,SAAS,CAAC;AAEzE,OAAO,MAAMC,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdc,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,MAAMG,QAAQ,GAAG,MAAMhB,GAAG,CAACiB,GAAG,CAAC,UAAU,CAAC;QAC1CR,OAAO,CAACO,QAAQ,CAACE,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;IAClC,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,KAAK,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAgB,KAAuB;IAC5E,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhB,GAAG,CAACwB,IAAI,CAAC,aAAa,EAAE;QAAEF,QAAQ;QAAEC;MAAS,CAAC,CAAC;MACtE,MAAM;QAAEE,YAAY;QAAEjB,IAAI,EAAEkB;MAAS,CAAC,GAAGV,QAAQ,CAACE,IAAI;MAEtDJ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEF,YAAY,CAAC;MAC3ChB,OAAO,CAACiB,QAAQ,CAAC;MACjB3B,OAAO,CAAC6B,OAAO,CAAC,MAAM,CAAC;MACvB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOT,KAAU,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACnB/B,OAAO,CAACoB,KAAK,CAAC,EAAAU,eAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;MACrD,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBlB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;IAChCX,OAAO,CAAC,IAAI,CAAC;IACbV,OAAO,CAAC6B,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,oBACE1B,OAAA,CAACC,WAAW,CAAC8B,QAAQ;IAACC,KAAK,EAAE;MAAE1B,IAAI;MAAEa,KAAK;MAAEW,MAAM;MAAEtB;IAAQ,CAAE;IAAAJ,QAAA,EAC3DA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/B,EAAA,CAhDWF,YAAqD;AAAAkC,EAAA,GAArDlC,YAAqD;AAkDlE,OAAO,MAAMmC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG9C,UAAU,CAACO,WAAW,CAAC;EACvC,IAAIuC,OAAO,KAAKtC,SAAS,EAAE;IACzB,MAAM,IAAIuC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}