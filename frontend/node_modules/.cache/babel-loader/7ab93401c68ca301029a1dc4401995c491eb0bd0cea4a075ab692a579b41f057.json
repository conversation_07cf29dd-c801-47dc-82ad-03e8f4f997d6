{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, message, Space, Tag, Popconfirm, Image, Spin } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst AccountManage = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadForm] = Form.useForm();\n  const platformOptions = [{\n    value: 'wechat_mp',\n    label: '微信公众号'\n  }, {\n    value: 'wechat_service',\n    label: '微信服务号'\n  }, {\n    value: 'xiaohongshu',\n    label: '小红书'\n  }];\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = account => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '操作失败');\n    }\n  };\n  const handleLogin = async account => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n  const startLoginStatusPolling = accountId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const {\n          logged_in\n        } = response.data;\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = account => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n\n    // 设置默认的日期范围（最近30天）\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      busi: 3,\n      tmpl: 19\n    });\n    setDownloadModalVisible(true);\n  };\n  const handleDownloadSubmit = async values => {\n    if (!downloadAccount) return;\n    setDownloadLoading(true);\n    try {\n      var _values$busi, _values$tmpl;\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: ((_values$busi = values.busi) === null || _values$busi === void 0 ? void 0 : _values$busi.toString()) || '3',\n        tmpl: ((_values$tmpl = values.tmpl) === null || _values$tmpl === void 0 ? void 0 : _values$tmpl.toString()) || '19'\n      });\n\n      // 创建下载链接\n      const downloadUrl = `/api/wechat/download-data/${downloadAccount.id}?${params}`;\n\n      // 创建临时链接并触发下载\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n\n      // 添加认证header（如果需要）\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            }\n          });\n          if (!response.ok) {\n            throw new Error(`下载失败: ${response.status}`);\n          }\n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n          link.href = url;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n  const columns = [{\n    title: '账号名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '平台类型',\n    dataIndex: 'platform',\n    key: 'platform',\n    render: platform => {\n      const option = platformOptions.find(opt => opt.value === platform);\n      return option ? option.label : platform;\n    }\n  }, {\n    title: '登录状态',\n    dataIndex: 'login_status',\n    key: 'login_status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status ? 'green' : 'red',\n      children: status ? '已登录' : '未登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后登录时间',\n    dataIndex: 'last_login_time',\n    key: 'last_login_time',\n    render: time => time ? new Date(time).toLocaleString() : '-'\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleLogin(record),\n        disabled: record.login_status,\n        children: \"\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"default\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDownload(record),\n        disabled: !record.login_status,\n        children: \"\\u4E0B\\u8F7D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8D26\\u53F7\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8D26\\u53F7\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 19\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u8D26\\u53F7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: accounts,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAccount ? '编辑账号' : '添加账号',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8D26\\u53F7\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入账号名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D26\\u53F7\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"platform\",\n          label: \"\\u5E73\\u53F0\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择平台类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E73\\u53F0\\u7C7B\\u578B\",\n            children: platformOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `登录 ${loginAccount === null || loginAccount === void 0 ? void 0 : loginAccount.name}`,\n      open: loginModalVisible,\n      onCancel: handleLoginModalClose,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoginModalClose,\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => loginAccount && handleLogin(loginAccount),\n        disabled: loginLoading || loginStatus === 'success',\n        children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801\"\n      }, \"retry\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this)],\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px 0'\n        },\n        children: [loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16\n            },\n            children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), loginStatus === 'scanning' && qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: qrCodeUrl,\n            alt: \"\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801\",\n            width: 200,\n            height: 200,\n            style: {\n              border: '1px solid #d9d9d9'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: 16,\n              color: '#1890ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), \" \\u8BF7\\u4F7F\\u7528\\u5FAE\\u4FE1\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\\u767B\\u5F55\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: \"\\u4E8C\\u7EF4\\u7801\\u5C06\\u572830\\u79D2\\u540E\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this), loginStatus === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#52c41a',\n              marginBottom: 16\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#52c41a',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u6210\\u529F\\uFF01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 13\n        }, this), loginStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              color: '#ff4d4f',\n              marginBottom: 16\n            },\n            children: \"\\u2717\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '16px'\n            },\n            children: \"\\u767B\\u5F55\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), loginStatus === 'waiting' && !loginLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {\n            style: {\n              fontSize: '48px',\n              color: '#d9d9d9',\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u51C6\\u5907\\u83B7\\u53D6\\u767B\\u5F55\\u4E8C\\u7EF4\\u7801...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `下载数据 - ${downloadAccount === null || downloadAccount === void 0 ? void 0 : downloadAccount.name}`,\n      open: downloadModalVisible,\n      onCancel: handleDownloadModalClose,\n      onOk: () => downloadForm.submit(),\n      okText: \"\\u4E0B\\u8F7D\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: downloadLoading,\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: downloadForm,\n        layout: \"vertical\",\n        onFinish: handleDownloadSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"start_date\",\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择开始日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"end_date\",\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择结束日期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\",\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65E5\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"busi\",\n          label: \"\\u4E1A\\u52A1\\u7C7B\\u578B\",\n          initialValue: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 3,\n              children: \"\\u9ED8\\u8BA4\\u4E1A\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u5176\\u4ED6\\u4E1A\\u52A12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tmpl\",\n          label: \"\\u6A21\\u677F\\u7C7B\\u578B\",\n          initialValue: 19,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: 19,\n              children: \"\\u9ED8\\u8BA4\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 1,\n              children: \"\\u6A21\\u677F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: 2,\n              children: \"\\u6A21\\u677F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f0f2f5',\n            padding: '12px',\n            borderRadius: '6px',\n            marginTop: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BF4\\u660E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '8px 0 0 0',\n              paddingLeft: '16px',\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u5C06\\u4E0B\\u8F7D\\u9009\\u5B9A\\u65F6\\u95F4\\u8303\\u56F4\\u5185\\u7684\\u5FAE\\u4FE1\\u516C\\u4F17\\u53F7\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u6587\\u4EF6\\u683C\\u5F0F\\u4E3AExcel(.xlsx)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u8BF7\\u786E\\u4FDD\\u8D26\\u53F7\\u5DF2\\u767B\\u5F55\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u4E0B\\u8F7D\\u65F6\\u95F4\\u53EF\\u80FD\\u8F83\\u957F\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountManage, \"MYyiVocI4FGSDXwcPJNT8LejMOE=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = AccountManage;\nexport default AccountManage;\nvar _c;\n$RefreshReg$(_c, \"AccountManage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Tag", "Popconfirm", "Image", "Spin", "PlusOutlined", "EditOutlined", "DeleteOutlined", "LoginOutlined", "QrcodeOutlined", "DownloadOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "AccountManage", "_s", "accounts", "setAccounts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingAccount", "setEditingAccount", "form", "useForm", "loginModalVisible", "setLoginModalVisible", "loginAccount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "qrCodeUrl", "setQrCodeUrl", "loginLoading", "setLoginLoading", "loginStatus", "setLoginStatus", "downloadModalVisible", "setDownloadModalVisible", "downloadAccount", "setDownloadAccount", "downloadLoading", "setDownloadLoading", "downloadForm", "platformOptions", "value", "label", "fetchAccounts", "response", "get", "data", "error", "handleAdd", "resetFields", "handleEdit", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "delete", "success", "handleSubmit", "values", "put", "post", "_error$response", "_error$response$data", "detail", "handleLogin", "qrcode", "startLoginStatusPolling", "_error$response2", "_error$response2$data", "accountId", "pollInterval", "setInterval", "logged_in", "clearInterval", "console", "setTimeout", "warning", "handleLoginModalClose", "handleDownload", "login_status", "endDate", "Date", "startDate", "setDate", "getDate", "start_date", "toISOString", "split", "end_date", "busi", "tmpl", "handleDownloadSubmit", "_values$busi", "_values$tmpl", "params", "URLSearchParams", "toString", "downloadUrl", "link", "document", "createElement", "href", "download", "token", "localStorage", "getItem", "fetch", "headers", "ok", "Error", "status", "blob", "url", "window", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDownloadModalClose", "columns", "title", "dataIndex", "key", "render", "platform", "option", "find", "opt", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "time", "toLocaleString", "_", "record", "size", "type", "icon", "onClick", "disabled", "onConfirm", "okText", "cancelText", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "layout", "onFinish", "<PERSON><PERSON>", "name", "rules", "required", "placeholder", "map", "footer", "width", "style", "textAlign", "padding", "marginTop", "src", "alt", "height", "border", "fontSize", "marginBottom", "confirmLoading", "initialValue", "background", "borderRadius", "margin", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Popconfirm,\n  Image,\n  Spin\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  platform: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n}\n\nconst AccountManage: React.FC = () => {\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingAccount, setEditingAccount] = useState<Account | null>(null);\n  const [form] = Form.useForm();\n\n  // 登录相关状态\n  const [loginModalVisible, setLoginModalVisible] = useState(false);\n  const [loginAccount, setLoginAccount] = useState<Account | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');\n  const [loginLoading, setLoginLoading] = useState(false);\n  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed\n\n  // 下载相关状态\n  const [downloadModalVisible, setDownloadModalVisible] = useState(false);\n  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  const [downloadForm] = Form.useForm();\n\n  const platformOptions = [\n    { value: 'wechat_mp', label: '微信公众号' },\n    { value: 'wechat_service', label: '微信服务号' },\n    { value: 'xiaohongshu', label: '小红书' },\n  ];\n\n  const fetchAccounts = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/accounts/');\n      setAccounts(response.data);\n    } catch (error) {\n      message.error('获取账号列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAccounts();\n  }, []);\n\n  const handleAdd = () => {\n    setEditingAccount(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (account: Account) => {\n    setEditingAccount(account);\n    form.setFieldsValue(account);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.delete(`/accounts/${id}`);\n      message.success('删除成功');\n      fetchAccounts();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingAccount) {\n        await api.put(`/accounts/${editingAccount.id}`, values);\n        message.success('更新成功');\n      } else {\n        await api.post('/accounts/', values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchAccounts();\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '操作失败');\n    }\n  };\n\n  const handleLogin = async (account: Account) => {\n    setLoginAccount(account);\n    setLoginModalVisible(true);\n    setLoginStatus('waiting');\n    setLoginLoading(true);\n\n    try {\n      // 获取登录二维码\n      const response = await api.post(`/wechat/login/qrcode/${account.id}`);\n      if (response.data.qrcode) {\n        setQrCodeUrl(response.data.qrcode);\n        setLoginStatus('scanning');\n\n        // 开始轮询登录状态\n        startLoginStatusPolling(account.id);\n      } else {\n        message.error('获取二维码失败');\n        setLoginStatus('failed');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取二维码失败');\n      setLoginStatus('failed');\n    } finally {\n      setLoginLoading(false);\n    }\n  };\n\n  const startLoginStatusPolling = (accountId: number) => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const response = await api.get(`/wechat/login/status/${accountId}`);\n        const { logged_in } = response.data;\n\n        if (logged_in) {\n          setLoginStatus('success');\n          message.success('登录成功！');\n          clearInterval(pollInterval);\n          setLoginModalVisible(false);\n          fetchAccounts(); // 刷新账号列表\n        }\n      } catch (error) {\n        console.error('检查登录状态失败:', error);\n      }\n    }, 3000); // 每3秒检查一次\n\n    // 30秒后停止轮询\n    setTimeout(() => {\n      clearInterval(pollInterval);\n      if (loginStatus === 'scanning') {\n        setLoginStatus('failed');\n        message.warning('登录超时，请重试');\n      }\n    }, 30000);\n  };\n\n  const handleLoginModalClose = () => {\n    setLoginModalVisible(false);\n    setLoginAccount(null);\n    setQrCodeUrl('');\n    setLoginStatus('waiting');\n  };\n\n  // 下载相关函数\n  const handleDownload = (account: Account) => {\n    if (!account.login_status) {\n      message.warning('请先登录账号后再下载数据');\n      return;\n    }\n    setDownloadAccount(account);\n    \n    // 设置默认的日期范围（最近30天）\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n    \n    downloadForm.setFieldsValue({\n      start_date: startDate.toISOString().split('T')[0],\n      end_date: endDate.toISOString().split('T')[0],\n      busi: 3,\n      tmpl: 19\n    });\n    \n    setDownloadModalVisible(true);\n  };\n\n  const handleDownloadSubmit = async (values: any) => {\n    if (!downloadAccount) return;\n    \n    setDownloadLoading(true);\n    try {\n      const params = new URLSearchParams({\n        start_date: values.start_date,\n        end_date: values.end_date,\n        busi: values.busi?.toString() || '3',\n        tmpl: values.tmpl?.toString() || '19'\n      });\n      \n      // 创建下载链接\n      const downloadUrl = `/api/wechat/download-data/${downloadAccount.id}?${params}`;\n      \n      // 创建临时链接并触发下载\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;\n      \n      // 添加认证header（如果需要）\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          // 使用fetch下载文件\n          const response = await fetch(downloadUrl, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            }\n          });\n          \n          if (!response.ok) {\n            throw new Error(`下载失败: ${response.status}`);\n          }\n          \n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n          \n          link.href = url;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          \n          message.success('数据下载成功');\n          setDownloadModalVisible(false);\n        } catch (error: any) {\n          console.error('下载错误:', error);\n          message.error(`下载失败: ${error.message}`);\n        }\n      } else {\n        message.error('请先登录系统');\n      }\n    } catch (error: any) {\n      console.error('下载错误:', error);\n      message.error('下载失败，请重试');\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n\n  const handleDownloadModalClose = () => {\n    setDownloadModalVisible(false);\n    setDownloadAccount(null);\n    downloadForm.resetFields();\n  };\n\n  const columns = [\n    {\n      title: '账号名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '平台类型',\n      dataIndex: 'platform',\n      key: 'platform',\n      render: (platform: string) => {\n        const option = platformOptions.find(opt => opt.value === platform);\n        return option ? option.label : platform;\n      },\n    },\n    {\n      title: '登录状态',\n      dataIndex: 'login_status',\n      key: 'login_status',\n      render: (status: boolean) => (\n        <Tag color={status ? 'green' : 'red'}>\n          {status ? '已登录' : '未登录'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'last_login_time',\n      key: 'last_login_time',\n      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: Account) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"primary\" \n            icon={<LoginOutlined />} \n            size=\"small\"\n            onClick={() => handleLogin(record)}\n            disabled={record.login_status}\n          >\n            登录\n          </Button>\n          <Button \n            type=\"default\"\n            icon={<DownloadOutlined />} \n            size=\"small\"\n            onClick={() => handleDownload(record)}\n            disabled={!record.login_status}\n          >\n            下载\n          </Button>\n          <Button \n            icon={<EditOutlined />} \n            size=\"small\"\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个账号吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              danger \n              icon={<DeleteOutlined />} \n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card \n        title=\"账号管理\" \n        extra={\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={handleAdd}\n          >\n            添加账号\n          </Button>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={accounts}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAccount ? '编辑账号' : '添加账号'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"账号名称\"\n            rules={[{ required: true, message: '请输入账号名称' }]}\n          >\n            <Input placeholder=\"请输入账号名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"platform\"\n            label=\"平台类型\"\n            rules={[{ required: true, message: '请选择平台类型' }]}\n          >\n            <Select placeholder=\"请选择平台类型\">\n              {platformOptions.map(option => (\n                <Option key={option.value} value={option.value}>\n                  {option.label}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 登录模态框 */}\n      <Modal\n        title={`登录 ${loginAccount?.name}`}\n        open={loginModalVisible}\n        onCancel={handleLoginModalClose}\n        footer={[\n          <Button key=\"close\" onClick={handleLoginModalClose}>\n            关闭\n          </Button>,\n          <Button\n            key=\"retry\"\n            type=\"primary\"\n            onClick={() => loginAccount && handleLogin(loginAccount)}\n            disabled={loginLoading || loginStatus === 'success'}\n          >\n            重新获取二维码\n          </Button>\n        ]}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          {loginLoading && (\n            <div>\n              <Spin size=\"large\" />\n              <p style={{ marginTop: 16 }}>正在获取二维码...</p>\n            </div>\n          )}\n\n          {loginStatus === 'scanning' && qrCodeUrl && (\n            <div>\n              <Image\n                src={qrCodeUrl}\n                alt=\"登录二维码\"\n                width={200}\n                height={200}\n                style={{ border: '1px solid #d9d9d9' }}\n              />\n              <p style={{ marginTop: 16, color: '#1890ff' }}>\n                <QrcodeOutlined /> 请使用微信扫描二维码登录\n              </p>\n              <p style={{ color: '#666', fontSize: '12px' }}>\n                二维码将在30秒后过期\n              </p>\n            </div>\n          )}\n\n          {loginStatus === 'success' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>\n                ✓\n              </div>\n              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>\n            </div>\n          )}\n\n          {loginStatus === 'failed' && (\n            <div>\n              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>\n                ✗\n              </div>\n              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>\n            </div>\n          )}\n\n          {loginStatus === 'waiting' && !loginLoading && (\n            <div>\n              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />\n              <p style={{ color: '#666' }}>准备获取登录二维码...</p>\n            </div>\n          )}\n        </div>\n      </Modal>\n\n      {/* 下载数据模态框 */}\n      <Modal\n        title={`下载数据 - ${downloadAccount?.name}`}\n        open={downloadModalVisible}\n        onCancel={handleDownloadModalClose}\n        onOk={() => downloadForm.submit()}\n        okText=\"下载\"\n        cancelText=\"取消\"\n        confirmLoading={downloadLoading}\n        width={500}\n      >\n        <Form\n          form={downloadForm}\n          layout=\"vertical\"\n          onFinish={handleDownloadSubmit}\n        >\n          <Form.Item\n            name=\"start_date\"\n            label=\"开始日期\"\n            rules={[{ required: true, message: '请选择开始日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择开始日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"end_date\"\n            label=\"结束日期\"\n            rules={[{ required: true, message: '请选择结束日期' }]}\n          >\n            <Input type=\"date\" placeholder=\"请选择结束日期\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"busi\"\n            label=\"业务类型\"\n            initialValue={3}\n          >\n            <Select>\n              <Option value={3}>默认业务</Option>\n              <Option value={1}>其他业务1</Option>\n              <Option value={2}>其他业务2</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"tmpl\"\n            label=\"模板类型\"\n            initialValue={19}\n          >\n            <Select>\n              <Option value={19}>默认模板</Option>\n              <Option value={1}>模板1</Option>\n              <Option value={2}>模板2</Option>\n            </Select>\n          </Form.Item>\n          \n          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>\n            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>\n              <strong>说明：</strong>\n            </p>\n            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>\n              <li>将下载选定时间范围内的微信公众号数据</li>\n              <li>文件格式为Excel(.xlsx)</li>\n              <li>请确保账号已登录状态</li>\n              <li>下载时间可能较长，请耐心等待</li>\n            </ul>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AccountManage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,QACC,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC/H,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAO,CAAC,GAAGhB,MAAM;AAWzB,MAAMiB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACmC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAiB,IAAI,CAAC;EACtE,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAS,SAAS,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC+C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAiB,IAAI,CAAC;EAC5E,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,YAAY,CAAC,GAAG/C,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAErC,MAAMkB,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAM,CAAC,CACvC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC3B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM4B,QAAQ,GAAG,MAAMrC,GAAG,CAACsC,GAAG,CAAC,YAAY,CAAC;MAC5C/B,WAAW,CAAC8B,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED7B,SAAS,CAAC,MAAM;IACdwD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtB5B,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAAC4B,WAAW,CAAC,CAAC;IAClB/B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgC,UAAU,GAAIC,OAAgB,IAAK;IACvC/B,iBAAiB,CAAC+B,OAAO,CAAC;IAC1B9B,IAAI,CAAC+B,cAAc,CAACD,OAAO,CAAC;IAC5BjC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmC,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM/C,GAAG,CAACgD,MAAM,CAAC,aAAaD,EAAE,EAAE,CAAC;MACnC3D,OAAO,CAAC6D,OAAO,CAAC,MAAM,CAAC;MACvBb,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAIvC,cAAc,EAAE;QAClB,MAAMZ,GAAG,CAACoD,GAAG,CAAC,aAAaxC,cAAc,CAACmC,EAAE,EAAE,EAAEI,MAAM,CAAC;QACvD/D,OAAO,CAAC6D,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMjD,GAAG,CAACqD,IAAI,CAAC,YAAY,EAAEF,MAAM,CAAC;QACpC/D,OAAO,CAAC6D,OAAO,CAAC,MAAM,CAAC;MACzB;MACAtC,eAAe,CAAC,KAAK,CAAC;MACtByB,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOI,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnBnE,OAAO,CAACoD,KAAK,CAAC,EAAAc,eAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;IACvD;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOb,OAAgB,IAAK;IAC9CzB,eAAe,CAACyB,OAAO,CAAC;IACxB3B,oBAAoB,CAAC,IAAI,CAAC;IAC1BQ,cAAc,CAAC,SAAS,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMc,QAAQ,GAAG,MAAMrC,GAAG,CAACqD,IAAI,CAAC,wBAAwBT,OAAO,CAACG,EAAE,EAAE,CAAC;MACrE,IAAIV,QAAQ,CAACE,IAAI,CAACmB,MAAM,EAAE;QACxBrC,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAACmB,MAAM,CAAC;QAClCjC,cAAc,CAAC,UAAU,CAAC;;QAE1B;QACAkC,uBAAuB,CAACf,OAAO,CAACG,EAAE,CAAC;MACrC,CAAC,MAAM;QACL3D,OAAO,CAACoD,KAAK,CAAC,SAAS,CAAC;QACxBf,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOe,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBzE,OAAO,CAACoD,KAAK,CAAC,EAAAoB,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,SAAS,CAAC;MACxD/B,cAAc,CAAC,QAAQ,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoC,uBAAuB,GAAIG,SAAiB,IAAK;IACrD,MAAMC,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMrC,GAAG,CAACsC,GAAG,CAAC,wBAAwBwB,SAAS,EAAE,CAAC;QACnE,MAAM;UAAEG;QAAU,CAAC,GAAG5B,QAAQ,CAACE,IAAI;QAEnC,IAAI0B,SAAS,EAAE;UACbxC,cAAc,CAAC,SAAS,CAAC;UACzBrC,OAAO,CAAC6D,OAAO,CAAC,OAAO,CAAC;UACxBiB,aAAa,CAACH,YAAY,CAAC;UAC3B9C,oBAAoB,CAAC,KAAK,CAAC;UAC3BmB,aAAa,CAAC,CAAC,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA4B,UAAU,CAAC,MAAM;MACfF,aAAa,CAACH,YAAY,CAAC;MAC3B,IAAIvC,WAAW,KAAK,UAAU,EAAE;QAC9BC,cAAc,CAAC,QAAQ,CAAC;QACxBrC,OAAO,CAACiF,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClCrD,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM8C,cAAc,GAAI3B,OAAgB,IAAK;IAC3C,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;MACzBpF,OAAO,CAACiF,OAAO,CAAC,cAAc,CAAC;MAC/B;IACF;IACAxC,kBAAkB,CAACe,OAAO,CAAC;;IAE3B;IACA,MAAM6B,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC1B,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;IAC5BC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE3C7C,YAAY,CAACa,cAAc,CAAC;MAC1BiC,UAAU,EAAEH,SAAS,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjDC,QAAQ,EAAER,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CE,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,CAAC;IAEFxD,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMyD,oBAAoB,GAAG,MAAOjC,MAAW,IAAK;IAClD,IAAI,CAACvB,eAAe,EAAE;IAEtBG,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MAAA,IAAAsD,YAAA,EAAAC,YAAA;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCV,UAAU,EAAE3B,MAAM,CAAC2B,UAAU;QAC7BG,QAAQ,EAAE9B,MAAM,CAAC8B,QAAQ;QACzBC,IAAI,EAAE,EAAAG,YAAA,GAAAlC,MAAM,CAAC+B,IAAI,cAAAG,YAAA,uBAAXA,YAAA,CAAaI,QAAQ,CAAC,CAAC,KAAI,GAAG;QACpCN,IAAI,EAAE,EAAAG,YAAA,GAAAnC,MAAM,CAACgC,IAAI,cAAAG,YAAA,uBAAXA,YAAA,CAAaG,QAAQ,CAAC,CAAC,KAAI;MACnC,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAW,GAAG,6BAA6B9D,eAAe,CAACmB,EAAE,IAAIwC,MAAM,EAAE;;MAE/E;MACA,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW;MACvBC,IAAI,CAACI,QAAQ,GAAG,eAAe5C,MAAM,CAAC2B,UAAU,OAAO3B,MAAM,CAAC8B,QAAQ,OAAO;;MAE7E;MACA,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACF;UACA,MAAM3D,QAAQ,GAAG,MAAM8D,KAAK,CAACT,WAAW,EAAE;YACxCU,OAAO,EAAE;cACP,eAAe,EAAE,UAAUJ,KAAK;YAClC;UACF,CAAC,CAAC;UAEF,IAAI,CAAC3D,QAAQ,CAACgE,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,SAASjE,QAAQ,CAACkE,MAAM,EAAE,CAAC;UAC7C;UAEA,MAAMC,IAAI,GAAG,MAAMnE,QAAQ,CAACmE,IAAI,CAAC,CAAC;UAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAE5Cb,IAAI,CAACG,IAAI,GAAGW,GAAG;UACfb,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAACnB,IAAI,CAAC;UAC/BA,IAAI,CAACoB,KAAK,CAAC,CAAC;UACZnB,QAAQ,CAACiB,IAAI,CAACG,WAAW,CAACrB,IAAI,CAAC;UAC/Be,MAAM,CAACC,GAAG,CAACM,eAAe,CAACR,GAAG,CAAC;UAE/BrH,OAAO,CAAC6D,OAAO,CAAC,QAAQ,CAAC;UACzBtB,uBAAuB,CAAC,KAAK,CAAC;QAChC,CAAC,CAAC,OAAOa,KAAU,EAAE;UACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;UAC7BpD,OAAO,CAACoD,KAAK,CAAC,SAASA,KAAK,CAACpD,OAAO,EAAE,CAAC;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,CAACoD,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRT,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMmF,wBAAwB,GAAGA,CAAA,KAAM;IACrCvF,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;IACxBG,YAAY,CAACU,WAAW,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMyE,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,QAAgB,IAAK;MAC5B,MAAMC,MAAM,GAAGxF,eAAe,CAACyF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzF,KAAK,KAAKsF,QAAQ,CAAC;MAClE,OAAOC,MAAM,GAAGA,MAAM,CAACtF,KAAK,GAAGqF,QAAQ;IACzC;EACF,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGhB,MAAe,iBACtBrG,OAAA,CAACZ,GAAG;MAACsI,KAAK,EAAErB,MAAM,GAAG,OAAO,GAAG,KAAM;MAAAsB,QAAA,EAClCtB,MAAM,GAAG,KAAK,GAAG;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAGW,IAAmB,IAAKA,IAAI,GAAG,IAAIxD,IAAI,CAACwD,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC5E,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGW,IAAY,IAAK,IAAIxD,IAAI,CAACwD,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACa,CAAM,EAAEC,MAAe,kBAC9BnI,OAAA,CAACb,KAAK;MAACiJ,IAAI,EAAC,QAAQ;MAAAT,QAAA,gBAClB3H,OAAA,CAACnB,MAAM;QACLwJ,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtI,OAAA,CAACL,aAAa;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC4E,MAAM,CAAE;QACnCK,QAAQ,EAAEL,MAAM,CAAC7D,YAAa;QAAAqD,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA,CAACnB,MAAM;QACLwJ,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtI,OAAA,CAACH,gBAAgB;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAAC8D,MAAM,CAAE;QACtCK,QAAQ,EAAE,CAACL,MAAM,CAAC7D,YAAa;QAAAqD,QAAA,EAChC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA,CAACnB,MAAM;QACLyJ,IAAI,eAAEtI,OAAA,CAACP,YAAY;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBK,IAAI,EAAC,OAAO;QACZG,OAAO,EAAEA,CAAA,KAAM9F,UAAU,CAAC0F,MAAM,CAAE;QAAAR,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA,CAACX,UAAU;QACT6H,KAAK,EAAC,oEAAa;QACnBuB,SAAS,EAAEA,CAAA,KAAM7F,YAAY,CAACuF,MAAM,CAACtF,EAAE,CAAE;QACzC6F,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhB,QAAA,eAEf3H,OAAA,CAACnB,MAAM;UACL+J,MAAM;UACNN,IAAI,eAAEtI,OAAA,CAACN,cAAc;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBK,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE/H,OAAA;IAAA2H,QAAA,gBACE3H,OAAA,CAACrB,IAAI;MACHuI,KAAK,EAAC,0BAAM;MACZ2B,KAAK,eACH7I,OAAA,CAACnB,MAAM;QACLwJ,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtI,OAAA,CAACR,YAAY;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAEhG,SAAU;QAAAoF,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAJ,QAAA,eAED3H,OAAA,CAACpB,KAAK;QACJqI,OAAO,EAAEA,OAAQ;QACjB6B,UAAU,EAAE1I,QAAS;QACrB2I,MAAM,EAAC,IAAI;QACXzI,OAAO,EAAEA,OAAQ;QACjB0I,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/H,OAAA,CAAClB,KAAK;MACJoI,KAAK,EAAExG,cAAc,GAAG,MAAM,GAAG,MAAO;MACxC2I,IAAI,EAAE7I,YAAa;MACnB8I,QAAQ,EAAEA,CAAA,KAAM7I,eAAe,CAAC,KAAK,CAAE;MACvC8I,IAAI,EAAEA,CAAA,KAAM3I,IAAI,CAAC4I,MAAM,CAAC,CAAE;MAC1Bd,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAhB,QAAA,eAEf3H,OAAA,CAACjB,IAAI;QACH6B,IAAI,EAAEA,IAAK;QACX6I,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE1G,YAAa;QAAA2E,QAAA,gBAEvB3H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,MAAM;UACX3H,KAAK,EAAC,0BAAM;UACZ4H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyI,QAAA,eAEhD3H,OAAA,CAAChB,KAAK;YAAC+K,WAAW,EAAC;UAAS;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZ/H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,UAAU;UACf3H,KAAK,EAAC,0BAAM;UACZ4H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyI,QAAA,eAEhD3H,OAAA,CAACf,MAAM;YAAC8K,WAAW,EAAC,4CAAS;YAAApC,QAAA,EAC1B5F,eAAe,CAACiI,GAAG,CAACzC,MAAM,iBACzBvH,OAAA,CAACC,MAAM;cAAoB+B,KAAK,EAAEuF,MAAM,CAACvF,KAAM;cAAA2F,QAAA,EAC5CJ,MAAM,CAACtF;YAAK,GADFsF,MAAM,CAACvF,KAAK;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/H,OAAA,CAAClB,KAAK;MACJoI,KAAK,EAAE,MAAMlG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4I,IAAI,EAAG;MAClCP,IAAI,EAAEvI,iBAAkB;MACxBwI,QAAQ,EAAElF,qBAAsB;MAChC6F,MAAM,EAAE,cACNjK,OAAA,CAACnB,MAAM;QAAa0J,OAAO,EAAEnE,qBAAsB;QAAAuD,QAAA,EAAC;MAEpD,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT/H,OAAA,CAACnB,MAAM;QAELwJ,IAAI,EAAC,SAAS;QACdE,OAAO,EAAEA,CAAA,KAAMvH,YAAY,IAAIuC,WAAW,CAACvC,YAAY,CAAE;QACzDwH,QAAQ,EAAEpH,YAAY,IAAIE,WAAW,KAAK,SAAU;QAAAqG,QAAA,EACrD;MAED,GANM,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CAAC,CACT;MACFmC,KAAK,EAAE,GAAI;MAAAvC,QAAA,eAEX3H,OAAA;QAAKmK,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAA1C,QAAA,GACpDvG,YAAY,iBACXpB,OAAA;UAAA2H,QAAA,gBACE3H,OAAA,CAACT,IAAI;YAAC6I,IAAI,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrB/H,OAAA;YAAGmK,KAAK,EAAE;cAAEG,SAAS,EAAE;YAAG,CAAE;YAAA3C,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,EAEAzG,WAAW,KAAK,UAAU,IAAIJ,SAAS,iBACtClB,OAAA;UAAA2H,QAAA,gBACE3H,OAAA,CAACV,KAAK;YACJiL,GAAG,EAAErJ,SAAU;YACfsJ,GAAG,EAAC,gCAAO;YACXN,KAAK,EAAE,GAAI;YACXO,MAAM,EAAE,GAAI;YACZN,KAAK,EAAE;cAAEO,MAAM,EAAE;YAAoB;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACF/H,OAAA;YAAGmK,KAAK,EAAE;cAAEG,SAAS,EAAE,EAAE;cAAE5C,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,gBAC5C3H,OAAA,CAACJ,cAAc;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/H,OAAA;YAAGmK,KAAK,EAAE;cAAEzC,KAAK,EAAE,MAAM;cAAEiD,QAAQ,EAAE;YAAO,CAAE;YAAAhD,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAzG,WAAW,KAAK,SAAS,iBACxBtB,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAKmK,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEjD,KAAK,EAAE,SAAS;cAAEkD,YAAY,EAAE;YAAG,CAAE;YAAAjD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/H,OAAA;YAAGmK,KAAK,EAAE;cAAEzC,KAAK,EAAE,SAAS;cAAEiD,QAAQ,EAAE;YAAO,CAAE;YAAAhD,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN,EAEAzG,WAAW,KAAK,QAAQ,iBACvBtB,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAKmK,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEjD,KAAK,EAAE,SAAS;cAAEkD,YAAY,EAAE;YAAG,CAAE;YAAAjD,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/H,OAAA;YAAGmK,KAAK,EAAE;cAAEzC,KAAK,EAAE,SAAS;cAAEiD,QAAQ,EAAE;YAAO,CAAE;YAAAhD,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN,EAEAzG,WAAW,KAAK,SAAS,IAAI,CAACF,YAAY,iBACzCpB,OAAA;UAAA2H,QAAA,gBACE3H,OAAA,CAACJ,cAAc;YAACuK,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEjD,KAAK,EAAE,SAAS;cAAEkD,YAAY,EAAE;YAAG;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnF/H,OAAA;YAAGmK,KAAK,EAAE;cAAEzC,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR/H,OAAA,CAAClB,KAAK;MACJoI,KAAK,EAAE,UAAUxF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkI,IAAI,EAAG;MACzCP,IAAI,EAAE7H,oBAAqB;MAC3B8H,QAAQ,EAAEtC,wBAAyB;MACnCuC,IAAI,EAAEA,CAAA,KAAMzH,YAAY,CAAC0H,MAAM,CAAC,CAAE;MAClCd,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfkC,cAAc,EAAEjJ,eAAgB;MAChCsI,KAAK,EAAE,GAAI;MAAAvC,QAAA,eAEX3H,OAAA,CAACjB,IAAI;QACH6B,IAAI,EAAEkB,YAAa;QACnB2H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExE,oBAAqB;QAAAyC,QAAA,gBAE/B3H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,YAAY;UACjB3H,KAAK,EAAC,0BAAM;UACZ4H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyI,QAAA,eAEhD3H,OAAA,CAAChB,KAAK;YAACqJ,IAAI,EAAC,MAAM;YAAC0B,WAAW,EAAC;UAAS;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZ/H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,UAAU;UACf3H,KAAK,EAAC,0BAAM;UACZ4H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5K,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyI,QAAA,eAEhD3H,OAAA,CAAChB,KAAK;YAACqJ,IAAI,EAAC,MAAM;YAAC0B,WAAW,EAAC;UAAS;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZ/H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,MAAM;UACX3H,KAAK,EAAC,0BAAM;UACZ6I,YAAY,EAAE,CAAE;UAAAnD,QAAA,eAEhB3H,OAAA,CAACf,MAAM;YAAA0I,QAAA,gBACL3H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,CAAE;cAAA2F,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/B/H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,CAAE;cAAA2F,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC/H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,CAAE;cAAA2F,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/H,OAAA,CAACjB,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,MAAM;UACX3H,KAAK,EAAC,0BAAM;UACZ6I,YAAY,EAAE,EAAG;UAAAnD,QAAA,eAEjB3H,OAAA,CAACf,MAAM;YAAA0I,QAAA,gBACL3H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,EAAG;cAAA2F,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC/H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,CAAE;cAAA2F,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9B/H,OAAA,CAACC,MAAM;cAAC+B,KAAK,EAAE,CAAE;cAAA2F,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/H,OAAA;UAAKmK,KAAK,EAAE;YAAEY,UAAU,EAAE,SAAS;YAAEV,OAAO,EAAE,MAAM;YAAEW,YAAY,EAAE,KAAK;YAAEV,SAAS,EAAE;UAAO,CAAE;UAAA3C,QAAA,gBAC7F3H,OAAA;YAAGmK,KAAK,EAAE;cAAEc,MAAM,EAAE,CAAC;cAAEN,QAAQ,EAAE,MAAM;cAAEjD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,eACvD3H,OAAA;cAAA2H,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJ/H,OAAA;YAAImK,KAAK,EAAE;cAAEc,MAAM,EAAE,WAAW;cAAEC,WAAW,EAAE,MAAM;cAAEP,QAAQ,EAAE,MAAM;cAAEjD,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACvF3H,OAAA;cAAA2H,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3B/H,OAAA;cAAA2H,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B/H,OAAA;cAAA2H,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB/H,OAAA;cAAA2H,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5H,EAAA,CA7gBID,aAAuB;EAAA,QAKZnB,IAAI,CAAC8B,OAAO,EAaJ9B,IAAI,CAAC8B,OAAO;AAAA;AAAAsK,EAAA,GAlB/BjL,aAAuB;AA+gB7B,eAAeA,aAAa;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}