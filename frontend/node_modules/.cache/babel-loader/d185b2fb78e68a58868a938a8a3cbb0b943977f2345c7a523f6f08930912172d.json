{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx\";\nimport React from 'react';\nimport { Card, Row, Col, Statistic, Typography } from 'antd';\nimport { UserOutlined, WechatOutlined, SyncOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6570\\u636E\\u6982\\u89C8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7BA1\\u7406\\u8D26\\u53F7\\u6570\",\n            value: 0,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u767B\\u5F55\\u8D26\\u53F7\",\n            value: 0,\n            prefix: /*#__PURE__*/_jsxDEV(WechatOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u540C\\u6B65\\u6B21\\u6570\",\n            value: 0,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6700\\u8FD1\\u6D3B\\u52A8\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Card", "Row", "Col", "Statistic", "Typography", "UserOutlined", "WechatOutlined", "SyncOutlined", "jsxDEV", "_jsxDEV", "Title", "Dashboard", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "style", "marginBottom", "span", "title", "value", "prefix", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, Typography, Table, Tag, message, Spin } from 'antd';\nimport { UserOutlined, WechatOutlined, SyncOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Title } = Typography;\n\nconst Dashboard: React.FC = () => {\n  return (\n    <div>\n      <Title level={2}>数据概览</Title>\n      \n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"管理账号数\"\n              value={0}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"已登录账号\"\n              value={0}\n              prefix={<WechatOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"今日同步次数\"\n              value={0}\n              prefix={<SyncOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card title=\"最近活动\" style={{ marginTop: 16 }}>\n        <p>暂无数据</p>\n      </Card>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAA+B,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,UAAU,QAAmC,MAAM;AACvF,SAASC,YAAY,EAAEC,cAAc,EAAEC,YAAY,QAA0B,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjG,MAAM;EAAEC;AAAM,CAAC,GAAGN,UAAU;AAE5B,MAAMO,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACEF,OAAA;IAAAG,QAAA,gBACEH,OAAA,CAACC,KAAK;MAACG,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BR,OAAA,CAACR,GAAG;MAACiB,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAR,QAAA,gBAC3CH,OAAA,CAACP,GAAG;QAACmB,IAAI,EAAE,CAAE;QAAAT,QAAA,eACXH,OAAA,CAACT,IAAI;UAAAY,QAAA,eACHH,OAAA,CAACN,SAAS;YACRmB,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAE;YACTC,MAAM,eAAEf,OAAA,CAACJ,YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNR,OAAA,CAACP,GAAG;QAACmB,IAAI,EAAE,CAAE;QAAAT,QAAA,eACXH,OAAA,CAACT,IAAI;UAAAY,QAAA,eACHH,OAAA,CAACN,SAAS;YACRmB,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAE;YACTC,MAAM,eAAEf,OAAA,CAACH,cAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNR,OAAA,CAACP,GAAG;QAACmB,IAAI,EAAE,CAAE;QAAAT,QAAA,eACXH,OAAA,CAACT,IAAI;UAAAY,QAAA,eACHH,OAAA,CAACN,SAAS;YACRmB,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAE,CAAE;YACTC,MAAM,eAAEf,OAAA,CAACF,YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAACT,IAAI;MAACsB,KAAK,EAAC,0BAAM;MAACH,KAAK,EAAE;QAAEM,SAAS,EAAE;MAAG,CAAE;MAAAb,QAAA,eAC1CH,OAAA;QAAAG,QAAA,EAAG;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACS,EAAA,GAxCIf,SAAmB;AA0CzB,eAAeA,SAAS;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}