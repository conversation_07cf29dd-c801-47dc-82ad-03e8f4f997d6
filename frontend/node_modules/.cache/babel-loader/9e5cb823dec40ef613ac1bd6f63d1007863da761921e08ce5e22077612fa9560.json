{"ast": null, "code": "// This icon file is generated automatically.\nvar StockOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M904 747H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM165.7 621.8l39.7 39.5c3.1 3.1 8.2 3.1 11.3 0l234.7-233.9 97.6 97.3a32.11 32.11 0 0045.2 0l264.2-263.2c3.1-3.1 3.1-8.2 0-11.3l-39.7-39.6a8.03 8.03 0 00-11.3 0l-235.7 235-97.7-97.3a32.11 32.11 0 00-45.2 0L165.7 610.5a7.94 7.94 0 000 11.3z\"\n      }\n    }]\n  },\n  \"name\": \"stock\",\n  \"theme\": \"outlined\"\n};\nexport default StockOutlined;", "map": {"version": 3, "names": ["StockOutlined"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/node_modules/@ant-design/icons-svg/es/asn/StockOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar StockOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M904 747H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM165.7 621.8l39.7 39.5c3.1 3.1 8.2 3.1 11.3 0l234.7-233.9 97.6 97.3a32.11 32.11 0 0045.2 0l264.2-263.2c3.1-3.1 3.1-8.2 0-11.3l-39.7-39.6a8.03 8.03 0 00-11.3 0l-235.7 235-97.7-97.3a32.11 32.11 0 00-45.2 0L165.7 610.5a7.94 7.94 0 000 11.3z\" } }] }, \"name\": \"stock\", \"theme\": \"outlined\" };\nexport default StockOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAyU;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,OAAO;EAAE,OAAO,EAAE;AAAW,CAAC;AAClhB,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}