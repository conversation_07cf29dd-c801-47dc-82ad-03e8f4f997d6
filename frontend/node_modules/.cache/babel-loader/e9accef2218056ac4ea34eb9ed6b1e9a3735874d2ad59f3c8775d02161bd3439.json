{"ast": null, "code": "export function newMessages() {\n  return {\n    default: 'Validation error on field %s',\n    required: '%s is required',\n    enum: '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid'\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      boolean: '%s is not a %s',\n      integer: '%s is not an %s',\n      float: '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s'\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters'\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s'\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length'\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s'\n    },\n    clone: function clone() {\n      var cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    }\n  };\n}\nexport var messages = newMessages();", "map": {"version": 3, "names": ["newMessages", "default", "required", "enum", "whitespace", "date", "format", "parse", "invalid", "types", "string", "method", "array", "object", "number", "boolean", "integer", "float", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch", "clone", "cloned", "JSON", "stringify", "messages"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/node_modules/@rc-component/async-validator/es/messages.js"], "sourcesContent": ["export function newMessages() {\n  return {\n    default: 'Validation error on field %s',\n    required: '%s is required',\n    enum: '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid'\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      boolean: '%s is not a %s',\n      integer: '%s is not an %s',\n      float: '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s'\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters'\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s'\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length'\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s'\n    },\n    clone: function clone() {\n      var cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    }\n  };\n}\nexport var messages = newMessages();"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAAA,EAAG;EAC5B,OAAO;IACLC,OAAO,EAAE,8BAA8B;IACvCC,QAAQ,EAAE,gBAAgB;IAC1BC,IAAI,EAAE,sBAAsB;IAC5BC,UAAU,EAAE,oBAAoB;IAChCC,IAAI,EAAE;MACJC,MAAM,EAAE,qCAAqC;MAC7CC,KAAK,EAAE,6CAA6C;MACpDC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,MAAM,EAAE,gBAAgB;MACxBC,MAAM,EAAE,2BAA2B;MACnCC,KAAK,EAAE,iBAAiB;MACxBC,MAAM,EAAE,iBAAiB;MACzBC,MAAM,EAAE,gBAAgB;MACxBT,IAAI,EAAE,gBAAgB;MACtBU,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,gBAAgB;MACvBC,MAAM,EAAE,sBAAsB;MAC9BC,KAAK,EAAE,sBAAsB;MAC7BC,GAAG,EAAE,sBAAsB;MAC3BC,GAAG,EAAE;IACP,CAAC;IACDX,MAAM,EAAE;MACNY,GAAG,EAAE,kCAAkC;MACvCC,GAAG,EAAE,mCAAmC;MACxCC,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE;IACT,CAAC;IACDX,MAAM,EAAE;MACNQ,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE,8BAA8B;MACnCC,KAAK,EAAE;IACT,CAAC;IACDb,KAAK,EAAE;MACLU,GAAG,EAAE,iCAAiC;MACtCC,GAAG,EAAE,qCAAqC;MAC1CC,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB,IAAIC,MAAM,GAAGC,IAAI,CAACvB,KAAK,CAACuB,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC;MAC7CF,MAAM,CAACD,KAAK,GAAG,IAAI,CAACA,KAAK;MACzB,OAAOC,MAAM;IACf;EACF,CAAC;AACH;AACA,OAAO,IAAIG,QAAQ,GAAGhC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}