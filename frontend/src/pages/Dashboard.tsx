import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Typography, Table, Tag, message, Spin } from 'antd';
import { UserOutlined, WechatOutlined, SyncOutlined, DatabaseOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Title } = Typography;

interface DashboardData {
  total_accounts: number;
  logged_in_accounts: number;
  total_data_records: number;
  recent_data: Array<{
    id: number;
    account_name: string;
    data_type: string;
    date: string;
    created_at: string;
  }>;
}

interface PlatformStats {
  [key: string]: {
    total_accounts: number;
    logged_in_accounts: number;
    data_records: number;
  };
}

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [platformStats, setPlatformStats] = useState<PlatformStats>({});
  const [loading, setLoading] = useState(true);

  const platformNames: { [key: string]: string } = {
    wechat_mp: '微信公众号',
    wechat_service: '微信服务号',
    xiaohongshu: '小红书'
  };

  const dataTypeNames: { [key: string]: string } = {
    user_summary: '用户数据',
    article_summary: '图文数据'
  };

  useEffect(() => {
    fetchDashboardData();
    fetchPlatformStats();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await api.get('/analytics/dashboard');
      setDashboardData(response.data);
    } catch (error) {
      message.error('获取概览数据失败');
    }
  };

  const fetchPlatformStats = async () => {
    try {
      const response = await api.get('/analytics/platform-stats');
      setPlatformStats(response.data);
    } catch (error) {
      message.error('获取平台统计失败');
    } finally {
      setLoading(false);
    }
  };

  const recentDataColumns = [
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
    },
    {
      title: '数据类型',
      dataIndex: 'data_type',
      key: 'data_type',
      render: (type: string) => (
        <Tag color="blue">{dataTypeNames[type] || type}</Tag>
      ),
    },
    {
      title: '数据日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '采集时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>数据概览</Title>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="管理账号数"
              value={dashboardData?.total_accounts || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已登录账号"
              value={dashboardData?.logged_in_accounts || 0}
              prefix={<WechatOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据记录数"
              value={dashboardData?.total_data_records || 0}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="最近活动"
              value={dashboardData?.recent_data?.length || 0}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="平台统计">
            <Row gutter={16}>
              {Object.entries(platformStats).map(([platform, stats]) => (
                <Col span={8} key={platform}>
                  <Card size="small" title={platformNames[platform] || platform}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>总账号: {stats.total_accounts}</span>
                      <span>已登录: {stats.logged_in_accounts}</span>
                      <span>数据: {stats.data_records}</span>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      <Card title="最近数据采集" style={{ marginTop: 16 }}>
        {dashboardData?.recent_data && dashboardData.recent_data.length > 0 ? (
          <Table
            columns={recentDataColumns}
            dataSource={dashboardData.recent_data}
            rowKey="id"
            pagination={{ pageSize: 5 }}
            size="small"
          />
        ) : (
          <p style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
            暂无数据采集记录
          </p>
        )}
      </Card>
    </div>
  );
};

export default Dashboard;