import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Space,
  Tag,
  Popconfirm,
  Image,
  Spin
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LoginOutlined, QrcodeOutlined, DownloadOutlined, LogoutOutlined, CloudSyncOutlined, TableOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Option } = Select;

interface Account {
  id: number;
  name: string;
  platform: string;
  login_status: boolean;
  last_login_time: string | null;
  created_at: string;
  feishu_app_token?: string;
  feishu_table_id?: string;
  feishu_created_at?: string;
}

const AccountManage: React.FC = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [form] = Form.useForm();

  // 登录相关状态
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [loginAccount, setLoginAccount] = useState<Account | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [loginLoading, setLoginLoading] = useState(false);
  const [loginStatus, setLoginStatus] = useState<string>('waiting'); // waiting, scanning, success, failed

  // 下载相关状态
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [downloadAccount, setDownloadAccount] = useState<Account | null>(null);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [downloadForm] = Form.useForm();

  // 注销相关状态
  const [logoutLoading, setLogoutLoading] = useState<number | null>(null); // 存储正在注销的账号ID

  // 飞书相关状态
  const [feishuModalVisible, setFeishuModalVisible] = useState(false);
  const [feishuAccount, setFeishuAccount] = useState<Account | null>(null);
  const [feishuLoading, setFeishuLoading] = useState(false);
  const [feishuSyncModalVisible, setFeishuSyncModalVisible] = useState(false);
  const [feishuSyncForm] = Form.useForm();

  const platformOptions = [
    { value: 'wechat_mp', label: '微信公众号' },
    { value: 'wechat_service', label: '微信服务号' },
    { value: 'xiaohongshu', label: '小红书' },
  ];

  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const response = await api.get('/accounts/');
      setAccounts(response.data);
    } catch (error) {
      message.error('获取账号列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  const handleAdd = () => {
    setEditingAccount(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (account: Account) => {
    setEditingAccount(account);
    form.setFieldsValue(account);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/accounts/${id}`);
      message.success('删除成功');
      fetchAccounts();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingAccount) {
        await api.put(`/accounts/${editingAccount.id}`, values);
        message.success('更新成功');
      } else {
        await api.post('/accounts/', values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchAccounts();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '操作失败');
    }
  };

  const handleLogin = async (account: Account) => {
    setLoginAccount(account);
    setLoginModalVisible(true);
    setLoginStatus('waiting');
    setLoginLoading(true);

    try {
      // 获取登录二维码
      const response = await api.post(`/wechat/login/qrcode/${account.id}`);
      if (response.data.qrcode) {
        setQrCodeUrl(response.data.qrcode);
        setLoginStatus('scanning');

        // 开始轮询登录状态
        startLoginStatusPolling(account.id);
      } else {
        message.error('获取二维码失败');
        setLoginStatus('failed');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取二维码失败');
      setLoginStatus('failed');
    } finally {
      setLoginLoading(false);
    }
  };

  const startLoginStatusPolling = (accountId: number) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await api.get(`/wechat/login/status/${accountId}`);
        const { logged_in } = response.data;

        if (logged_in) {
          setLoginStatus('success');
          message.success('登录成功！');
          clearInterval(pollInterval);
          setLoginModalVisible(false);
          fetchAccounts(); // 刷新账号列表
        }
      } catch (error) {
        console.error('检查登录状态失败:', error);
      }
    }, 3000); // 每3秒检查一次

    // 30秒后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
      if (loginStatus === 'scanning') {
        setLoginStatus('failed');
        message.warning('登录超时，请重试');
      }
    }, 30000);
  };

  const handleLoginModalClose = () => {
    setLoginModalVisible(false);
    setLoginAccount(null);
    setQrCodeUrl('');
    setLoginStatus('waiting');
  };

  // 下载相关函数
  const handleDownload = (account: Account) => {
    if (!account.login_status) {
      message.warning('请先登录账号后再下载数据');
      return;
    }
    setDownloadAccount(account);
    
    // 设置默认的日期范围（前一天结束，向前30天）
    const today = new Date();
    const endDate = new Date(today);
    endDate.setDate(today.getDate() - 1); // 前一天
    
    const startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天
    
    downloadForm.setFieldsValue({
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      busi: 3,
      tmpl: 19
    });
    
    setDownloadModalVisible(true);
  };

  const handleDownloadSubmit = async (values: any) => {
    if (!downloadAccount) return;
    
    setDownloadLoading(true);
    try {
      const params = new URLSearchParams({
        start_date: values.start_date,
        end_date: values.end_date,
        busi: values.busi?.toString() || '3',
        tmpl: values.tmpl?.toString() || '19'
      });
      
      // 使用相对路径，让axios的baseURL生效
      const downloadUrl = `http://localhost:8000/api/wechat/download-data/${downloadAccount.id}?${params}`;
      
      const token = localStorage.getItem('token');
      if (token) {
        try {
          console.log('下载URL:', downloadUrl);
          
          // 使用fetch下载文件
          const response = await fetch(downloadUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          console.log('响应状态:', response.status);
          console.log('响应头:', Object.fromEntries(response.headers.entries()));
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('错误响应:', errorText);
            throw new Error(`下载失败: ${response.status} - ${errorText}`);
          }
          
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          
          // 创建下载链接
          const link = document.createElement('a');
          link.href = url;
          link.download = `wechat_data_${values.start_date}_to_${values.end_date}.xlsx`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          
          message.success('数据下载成功');
          message.success('数据下载成功');
          setDownloadModalVisible(false);
        } catch (error: any) {
          console.error('下载错误:', error);
          message.error(`下载失败: ${error.message}`);
        }
      } else {
        message.error('请先登录系统');
      }
    } catch (error: any) {
      console.error('下载错误:', error);
      message.error('下载失败，请重试');
    } finally {
      setDownloadLoading(false);
    }
  };

  const handleDownloadModalClose = () => {
    setDownloadModalVisible(false);
    setDownloadAccount(null);
    downloadForm.resetFields();
  };

  // 注销处理函数
  const handleLogout = async (account: Account) => {
    try {
      setLogoutLoading(account.id);
      
      const response = await api.post(`/wechat/logout/${account.id}`, {
        clear_saved_state: true
      });

      if (response.data.success) {
        message.success(`账号 ${account.name} 注销成功`);
        // 刷新账号列表
        fetchAccounts();
      } else {
        message.warning(`账号 ${account.name} 注销完成，但可能存在部分问题`);
        // 仍然刷新列表
        fetchAccounts();
      }
    } catch (error: any) {
      console.error('注销失败:', error);
      message.error(`注销失败: ${error.response?.data?.detail || '未知错误'}`);
    } finally {
      setLogoutLoading(null);
    }
  };

  // 强制注销处理函数
  const handleForceLogout = async (account: Account) => {
    try {
      setLogoutLoading(account.id);
      
      const response = await api.post(`/wechat/force-logout/${account.id}`);

      if (response.data.success) {
        message.success(`账号 ${account.name} 强制注销成功`);
      } else {
        message.warning(`账号 ${account.name} 强制注销完成`);
      }
      
      // 刷新账号列表
      fetchAccounts();
    } catch (error: any) {
      console.error('强制注销失败:', error);
      message.error(`强制注销失败: ${error.response?.data?.detail || '未知错误'}`);
    } finally {
      setLogoutLoading(null);
    }
  };

  // 批量注销所有账号
  const handleLogoutAll = async () => {
    try {
      setLoading(true);

      const response = await api.get('/wechat/logout-all');

      const { success, message: msg, logout_results } = response.data;

      if (success) {
        message.success(msg);
      } else {
        message.warning(msg);
      }

      // 显示详细结果
      if (logout_results && logout_results.length > 0) {
        const successCount = logout_results.filter((r: any) => r.success).length;
        const totalCount = logout_results.length;

        if (successCount === totalCount) {
          message.success(`所有 ${totalCount} 个账号注销成功`);
        } else {
          message.warning(`${successCount}/${totalCount} 个账号注销成功`);
        }
      }

      // 刷新账号列表
      fetchAccounts();
    } catch (error: any) {
      console.error('批量注销失败:', error);
      message.error(`批量注销失败: ${error.response?.data?.detail || '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 飞书相关处理函数
  const handleCreateFeishuBitable = async (account: Account) => {
    try {
      setFeishuLoading(true);

      const response = await api.post(`/feishu/create-bitable/${account.id}`, {
        bitable_name: `${account.name}_数据分析表格`
      });

      if (response.data.success) {
        message.success('飞书多维表格创建成功！');
        fetchAccounts(); // 刷新账号列表
      } else {
        message.error('创建飞书多维表格失败');
      }
    } catch (error: any) {
      console.error('创建飞书表格失败:', error);
      message.error(`创建失败: ${error.response?.data?.detail || '未知错误'}`);
    } finally {
      setFeishuLoading(false);
    }
  };

  const handleSyncToFeishu = (account: Account) => {
    if (!account.feishu_app_token) {
      message.warning('请先创建飞书多维表格');
      return;
    }
    if (!account.login_status) {
      message.warning('请先登录微信公众号');
      return;
    }

    setFeishuAccount(account);

    // 设置默认的日期范围（前一天结束，向前30天）
    const today = new Date();
    const endDate = new Date(today);
    endDate.setDate(today.getDate() - 1); // 前一天

    const startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天

    feishuSyncForm.setFieldsValue({
      begin_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    });

    setFeishuSyncModalVisible(true);
  };

  const handleFeishuSyncSubmit = async (values: any) => {
    if (!feishuAccount) return;

    try {
      setFeishuLoading(true);

      const response = await api.post(`/feishu/sync-data/${feishuAccount.id}`, {
        begin_date: values.begin_date,
        end_date: values.end_date
      });

      if (response.data.success) {
        const { sync_results, data_summary } = response.data;
        message.success(
          `数据同步成功！用户数据: ${sync_results.user_data_synced} 条，图文数据: ${sync_results.article_data_synced} 条`
        );
        setFeishuSyncModalVisible(false);
      } else {
        message.error('数据同步失败');
      }
    } catch (error: any) {
      console.error('同步数据失败:', error);
      message.error(`同步失败: ${error.response?.data?.detail || '未知错误'}`);
    } finally {
      setFeishuLoading(false);
    }
  };

  const handleFeishuSyncModalClose = () => {
    setFeishuSyncModalVisible(false);
    setFeishuAccount(null);
    feishuSyncForm.resetFields();
  };

  const columns = [
    {
      title: '账号名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '平台类型',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform: string) => {
        const option = platformOptions.find(opt => opt.value === platform);
        return option ? option.label : platform;
      },
    },
    {
      title: '登录状态',
      dataIndex: 'login_status',
      key: 'login_status',
      render: (status: boolean) => (
        <Tag color={status ? 'green' : 'red'}>
          {status ? '已登录' : '未登录'}
        </Tag>
      ),
    },
    {
      title: '最后登录时间',
      dataIndex: 'last_login_time',
      key: 'last_login_time',
      render: (time: string | null) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '飞书状态',
      key: 'feishu_status',
      render: (_: any, record: Account) => (
        <Tag color={record.feishu_app_token ? 'blue' : 'default'}>
          {record.feishu_app_token ? '已创建' : '未创建'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Account) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<LoginOutlined />} 
            size="small"
            onClick={() => handleLogin(record)}
            disabled={record.login_status}
          >
            登录
          </Button>
          <Button
            type="default"
            icon={<DownloadOutlined />}
            size="small"
            onClick={() => handleDownload(record)}
            disabled={!record.login_status}
          >
            下载
          </Button>
          {!record.feishu_app_token ? (
            <Button
              type="default"
              icon={<TableOutlined />}
              size="small"
              onClick={() => handleCreateFeishuBitable(record)}
              loading={feishuLoading}
              disabled={feishuLoading}
            >
              创建飞书表格
            </Button>
          ) : (
            <Button
              type="default"
              icon={<CloudSyncOutlined />}
              size="small"
              onClick={() => handleSyncToFeishu(record)}
              disabled={!record.login_status || feishuLoading}
              loading={feishuLoading}
            >
              同步到飞书
            </Button>
          )}
          <Popconfirm
            title="确定要注销这个账号的登录状态吗？"
            description="注销后需要重新扫码登录"
            onConfirm={() => handleLogout(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="default"
              icon={<LogoutOutlined />} 
              size="small"
              loading={logoutLoading === record.id}
              disabled={!record.login_status || logoutLoading === record.id}
            >
              注销
            </Button>
          </Popconfirm>
          <Button 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个账号吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card 
        title="账号管理" 
        extra={
          <Space>
            <Popconfirm
              title="确定要注销所有账号的登录状态吗？"
              description="这将注销当前用户的所有微信公众号账号登录状态"
              onConfirm={handleLogoutAll}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="default"
                icon={<LogoutOutlined />}
                loading={loading}
                disabled={accounts.length === 0 || !accounts.some(acc => acc.login_status)}
              >
                注销全部
              </Button>
            </Popconfirm>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加账号
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingAccount ? '编辑账号' : '添加账号'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="账号名称"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input placeholder="请输入账号名称" />
          </Form.Item>
          <Form.Item
            name="platform"
            label="平台类型"
            rules={[{ required: true, message: '请选择平台类型' }]}
          >
            <Select placeholder="请选择平台类型">
              {platformOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 登录模态框 */}
      <Modal
        title={`登录 ${loginAccount?.name}`}
        open={loginModalVisible}
        onCancel={handleLoginModalClose}
        footer={[
          <Button key="close" onClick={handleLoginModalClose}>
            关闭
          </Button>,
          <Button
            key="retry"
            type="primary"
            onClick={() => loginAccount && handleLogin(loginAccount)}
            disabled={loginLoading || loginStatus === 'success'}
          >
            重新获取二维码
          </Button>
        ]}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          {loginLoading && (
            <div>
              <Spin size="large" />
              <p style={{ marginTop: 16 }}>正在获取二维码...</p>
            </div>
          )}

          {loginStatus === 'scanning' && qrCodeUrl && (
            <div>
              <Image
                src={qrCodeUrl}
                alt="登录二维码"
                width={200}
                height={200}
                style={{ border: '1px solid #d9d9d9' }}
              />
              <p style={{ marginTop: 16, color: '#1890ff' }}>
                <QrcodeOutlined /> 请使用微信扫描二维码登录
              </p>
              <p style={{ color: '#666', fontSize: '12px' }}>
                二维码将在30秒后过期
              </p>
            </div>
          )}

          {loginStatus === 'success' && (
            <div>
              <div style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }}>
                ✓
              </div>
              <p style={{ color: '#52c41a', fontSize: '16px' }}>登录成功！</p>
            </div>
          )}

          {loginStatus === 'failed' && (
            <div>
              <div style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: 16 }}>
                ✗
              </div>
              <p style={{ color: '#ff4d4f', fontSize: '16px' }}>登录失败，请重试</p>
            </div>
          )}

          {loginStatus === 'waiting' && !loginLoading && (
            <div>
              <QrcodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />
              <p style={{ color: '#666' }}>准备获取登录二维码...</p>
            </div>
          )}
        </div>
      </Modal>

      {/* 下载数据模态框 */}
      <Modal
        title={`下载数据 - ${downloadAccount?.name}`}
        open={downloadModalVisible}
        onCancel={handleDownloadModalClose}
        onOk={() => downloadForm.submit()}
        okText="下载"
        cancelText="取消"
        confirmLoading={downloadLoading}
        width={500}
      >
        <Form
          form={downloadForm}
          layout="vertical"
          onFinish={handleDownloadSubmit}
        >
          <Form.Item
            name="start_date"
            label="开始日期"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <Input type="date" placeholder="请选择开始日期" />
          </Form.Item>
          
          <Form.Item
            name="end_date"
            label="结束日期"
            rules={[{ required: true, message: '请选择结束日期' }]}
          >
            <Input type="date" placeholder="请选择结束日期" />
          </Form.Item>
          
          <Form.Item
            name="busi"
            label="业务类型"
            initialValue={3}
          >
            <Select>
              <Option value={3}>默认业务</Option>
              <Option value={1}>其他业务1</Option>
              <Option value={2}>其他业务2</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tmpl"
            label="模板类型"
            initialValue={19}
          >
            <Select>
              <Option value={19}>默认模板</Option>
              <Option value={1}>模板1</Option>
              <Option value={2}>模板2</Option>
            </Select>
          </Form.Item>
          
          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>
            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
              <strong>说明：</strong>
            </p>
            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>
              <li>将下载选定时间范围内的微信公众号数据</li>
              <li>默认选择前一天结束，向前30天的数据范围</li>
              <li>文件格式为Excel(.xlsx)</li>
              <li>请确保账号已登录状态</li>
              <li>下载时间可能较长，请耐心等待</li>
            </ul>
          </div>
        </Form>
      </Modal>

      {/* 飞书数据同步模态框 */}
      <Modal
        title={`同步数据到飞书 - ${feishuAccount?.name}`}
        open={feishuSyncModalVisible}
        onCancel={handleFeishuSyncModalClose}
        onOk={() => feishuSyncForm.submit()}
        okText="开始同步"
        cancelText="取消"
        confirmLoading={feishuLoading}
        width={500}
      >
        <Form
          form={feishuSyncForm}
          layout="vertical"
          onFinish={handleFeishuSyncSubmit}
        >
          <Form.Item
            name="begin_date"
            label="开始日期"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <Input type="date" placeholder="请选择开始日期" />
          </Form.Item>

          <Form.Item
            name="end_date"
            label="结束日期"
            rules={[{ required: true, message: '请选择结束日期' }]}
          >
            <Input type="date" placeholder="请选择结束日期" />
          </Form.Item>

          <div style={{ background: '#f0f2f5', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>
            <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
              <strong>说明：</strong>
            </p>
            <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px', fontSize: '12px', color: '#666' }}>
              <li>将下载微信公众号数据并自动同步到飞书多维表格</li>
              <li>默认选择前一天结束，向前30天的数据范围</li>
              <li>会自动创建用户概况和图文分析两个数据表</li>
              <li>请确保账号已登录且已创建飞书表格</li>
              <li>同步时间可能较长，请耐心等待</li>
            </ul>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AccountManage;
