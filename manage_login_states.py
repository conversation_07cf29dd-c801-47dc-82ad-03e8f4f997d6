#!/usr/bin/env python3
"""
微信公众号登录状态管理工具
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path

class LoginStateManager:
    def __init__(self, base_dir: str = "user_data"):
        self.base_dir = Path(base_dir)
        
    def list_saved_states(self):
        """列出所有已保存的登录状态"""
        print("=== 已保存的登录状态 ===")
        
        if not self.base_dir.exists():
            print("没有找到任何保存的登录状态")
            return
        
        found_any = False
        for account_dir in self.base_dir.iterdir():
            if account_dir.is_dir():
                state_file = account_dir / "login_state.json"
                if state_file.exists():
                    try:
                        with open(state_file, 'r', encoding='utf-8') as f:
                            state_data = json.load(f)
                        
                        account_id = state_data.get('account_id', 'unknown')
                        saved_at = state_data.get('saved_at', '')
                        
                        if saved_at:
                            saved_time = datetime.fromisoformat(saved_at)
                            time_str = saved_time.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            time_str = '未知时间'
                        
                        print(f"📁 账号ID: {account_id}")
                        print(f"   保存时间: {time_str}")
                        print(f"   状态文件: {state_file}")
                        print(f"   目录: {account_dir}")
                        print()
                        
                        found_any = True
                        
                    except Exception as e:
                        print(f"❌ 读取状态文件失败 {state_file}: {e}")
        
        if not found_any:
            print("没有找到任何有效的登录状态")
    
    def clear_state(self, account_id: int = None):
        """清除指定账号或所有账号的登录状态"""
        if account_id:
            # 清除指定账号
            account_dir = self.base_dir / f"wechat_account_{account_id}"
            if account_dir.exists():
                try:
                    shutil.rmtree(account_dir)
                    print(f"✅ 已清除账号 {account_id} 的登录状态")
                except Exception as e:
                    print(f"❌ 清除账号 {account_id} 登录状态失败: {e}")
            else:
                print(f"ℹ️  账号 {account_id} 没有保存的登录状态")
        else:
            # 清除所有状态
            if self.base_dir.exists():
                try:
                    shutil.rmtree(self.base_dir)
                    print("✅ 已清除所有保存的登录状态")
                except Exception as e:
                    print(f"❌ 清除所有登录状态失败: {e}")
            else:
                print("ℹ️  没有找到任何保存的登录状态")
    
    def backup_states(self, backup_dir: str = "login_states_backup"):
        """备份所有登录状态"""
        backup_path = Path(backup_dir)
        
        if not self.base_dir.exists():
            print("没有找到任何需要备份的登录状态")
            return
        
        try:
            # 创建备份目录
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_full_path = backup_path / f"backup_{timestamp}"
            backup_full_path.mkdir(parents=True, exist_ok=True)
            
            # 复制所有状态文件
            shutil.copytree(self.base_dir, backup_full_path / "user_data")
            
            print(f"✅ 登录状态已备份到: {backup_full_path}")
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
    
    def restore_states(self, backup_path: str):
        """从备份恢复登录状态"""
        backup_dir = Path(backup_path)
        
        if not backup_dir.exists():
            print(f"❌ 备份目录不存在: {backup_path}")
            return
        
        source_dir = backup_dir / "user_data"
        if not source_dir.exists():
            print(f"❌ 备份目录中没有找到user_data: {source_dir}")
            return
        
        try:
            # 如果目标目录存在，先删除
            if self.base_dir.exists():
                shutil.rmtree(self.base_dir)
            
            # 恢复备份
            shutil.copytree(source_dir, self.base_dir)
            
            print(f"✅ 登录状态已从 {backup_path} 恢复")
            
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
    
    def check_state_validity(self, account_id: int = None):
        """检查登录状态的有效性"""
        print("=== 检查登录状态有效性 ===")
        
        if account_id:
            # 检查指定账号
            account_dirs = [self.base_dir / f"wechat_account_{account_id}"]
        else:
            # 检查所有账号
            if not self.base_dir.exists():
                print("没有找到任何登录状态")
                return
            account_dirs = [d for d in self.base_dir.iterdir() if d.is_dir()]
        
        for account_dir in account_dirs:
            state_file = account_dir / "login_state.json"
            if state_file.exists():
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        state_data = json.load(f)
                    
                    account_id = state_data.get('account_id', 'unknown')
                    saved_at = state_data.get('saved_at', '')
                    
                    if saved_at:
                        saved_time = datetime.fromisoformat(saved_at)
                        age = datetime.now() - saved_time
                        
                        print(f"📁 账号ID: {account_id}")
                        print(f"   保存时间: {saved_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        print(f"   状态年龄: {age.days}天 {age.seconds//3600}小时")
                        
                        if age.days > 7:
                            print("   ⚠️  状态可能已过期（超过7天）")
                        elif age.days > 3:
                            print("   ⚠️  状态即将过期（超过3天）")
                        else:
                            print("   ✅ 状态较新")
                        print()
                    
                except Exception as e:
                    print(f"❌ 检查状态文件失败 {state_file}: {e}")

def main():
    """主函数"""
    manager = LoginStateManager()
    
    while True:
        print("\n=== 微信公众号登录状态管理工具 ===")
        print("1. 列出所有保存的登录状态")
        print("2. 清除指定账号的登录状态")
        print("3. 清除所有登录状态")
        print("4. 备份登录状态")
        print("5. 从备份恢复登录状态")
        print("6. 检查登录状态有效性")
        print("7. 注销指定账号登录状态")
        print("8. 强制注销指定账号")
        print("9. 注销所有账号登录状态")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-9): ").strip()
        
        if choice == "0":
            print("再见！")
            break
        elif choice == "1":
            manager.list_saved_states()
        elif choice == "2":
            try:
                account_id = int(input("请输入账号ID: "))
                manager.clear_state(account_id)
            except ValueError:
                print("❌ 请输入有效的账号ID")
        elif choice == "3":
            confirm = input("确认清除所有登录状态？(y/N): ").strip().lower()
            if confirm == 'y':
                manager.clear_state()
            else:
                print("操作已取消")
        elif choice == "4":
            backup_dir = input("备份目录路径 (默认: login_states_backup): ").strip()
            if not backup_dir:
                backup_dir = "login_states_backup"
            manager.backup_states(backup_dir)
        elif choice == "5":
            backup_path = input("备份路径: ").strip()
            if backup_path:
                manager.restore_states(backup_path)
            else:
                print("❌ 请提供备份路径")
        elif choice == "6":
            account_input = input("账号ID (留空检查所有): ").strip()
            if account_input:
                try:
                    account_id = int(account_input)
                    manager.check_state_validity(account_id)
                except ValueError:
                    print("❌ 请输入有效的账号ID")
            else:
                manager.check_state_validity()
        elif choice == "7":
            try:
                account_id = int(input("请输入要注销的账号ID: "))
                await logout_account(account_id)
            except ValueError:
                print("❌ 请输入有效的账号ID")
        elif choice == "8":
            try:
                account_id = int(input("请输入要强制注销的账号ID: "))
                await force_logout_account(account_id)
            except ValueError:
                print("❌ 请输入有效的账号ID")
        elif choice == "9":
            confirm = input("确认注销所有账号的登录状态？(y/N): ").strip().lower()
            if confirm == 'y':
                await logout_all_accounts()
            else:
                print("操作已取消")
        else:
            print("❌ 无效选择")

async def logout_account(account_id: int):
    """注销指定账号的登录状态"""
    try:
        # 导入必要的模块
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from app.services.wechat_service import WeChatMPService
        
        print(f"开始注销账号 {account_id}...")
        
        wechat_service = WeChatMPService(account_id=account_id)
        
        # 尝试加载并注销
        has_state = await wechat_service.load_login_state()
        if has_state:
            print("找到已保存的登录状态，开始注销...")
            logout_success = await wechat_service.logout(clear_saved_state=True)
            
            if logout_success:
                print(f"✅ 账号 {account_id} 注销成功")
            else:
                print(f"⚠️ 账号 {account_id} 注销完成，但可能存在部分问题")
        else:
            print(f"ℹ️ 账号 {account_id} 没有找到登录状态")
            # 仍然尝试清理可能存在的文件
            wechat_service._clear_saved_login_state()
            print(f"✅ 已清理账号 {account_id} 的相关文件")
        
    except Exception as e:
        print(f"❌ 注销账号 {account_id} 失败: {e}")
    finally:
        try:
            await wechat_service.close()
        except:
            pass

async def force_logout_account(account_id: int):
    """强制注销指定账号"""
    try:
        # 导入必要的模块
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from app.services.wechat_service import WeChatMPService
        
        print(f"开始强制注销账号 {account_id}...")
        
        wechat_service = WeChatMPService(account_id=account_id)
        force_logout_success = await wechat_service.force_logout()
        
        if force_logout_success:
            print(f"✅ 账号 {account_id} 强制注销成功")
        else:
            print(f"⚠️ 账号 {account_id} 强制注销完成")
        
    except Exception as e:
        print(f"❌ 强制注销账号 {account_id} 失败: {e}")

async def logout_all_accounts():
    """注销所有账号的登录状态"""
    try:
        # 导入必要的模块
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from app.services.wechat_service import WeChatMPService
        
        base_dir = Path("user_data")
        if not base_dir.exists():
            print("ℹ️ 没有找到任何登录状态")
            return
        
        account_dirs = [d for d in base_dir.iterdir() if d.is_dir() and d.name.startswith("wechat_account_")]
        
        if not account_dirs:
            print("ℹ️ 没有找到任何微信账号登录状态")
            return
        
        print(f"找到 {len(account_dirs)} 个账号，开始批量注销...")
        
        success_count = 0
        for account_dir in account_dirs:
            try:
                # 从目录名提取账号ID
                account_id_str = account_dir.name.replace("wechat_account_", "")
                account_id = int(account_id_str)
                
                print(f"注销账号 {account_id}...")
                
                wechat_service = WeChatMPService(account_id=account_id)
                logout_success = await wechat_service.force_logout()
                
                if logout_success:
                    print(f"✅ 账号 {account_id} 注销成功")
                    success_count += 1
                else:
                    print(f"⚠️ 账号 {account_id} 注销完成")
                    success_count += 1  # 强制注销总是认为成功
                
            except Exception as e:
                print(f"❌ 注销账号 {account_dir.name} 失败: {e}")
        
        print(f"\n批量注销完成: {success_count}/{len(account_dirs)} 个账号处理成功")
        
    except Exception as e:
        print(f"❌ 批量注销失败: {e}")

def main():
    """主函数"""
    manager = LoginStateManager()
    
    # 检查是否需要异步操作
    import asyncio
    
    async def async_main():
        while True:
            print("\n=== 微信公众号登录状态管理工具 ===")
            print("1. 列出所有保存的登录状态")
            print("2. 清除指定账号的登录状态")
            print("3. 清除所有登录状态")
            print("4. 备份登录状态")
            print("5. 从备份恢复登录状态")
            print("6. 检查登录状态有效性")
            print("7. 注销指定账号登录状态")
            print("8. 强制注销指定账号")
            print("9. 注销所有账号登录状态")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == "0":
                print("再见！")
                break
            elif choice == "1":
                manager.list_saved_states()
            elif choice == "2":
                try:
                    account_id = int(input("请输入账号ID: "))
                    manager.clear_state(account_id)
                except ValueError:
                    print("❌ 请输入有效的账号ID")
            elif choice == "3":
                confirm = input("确认清除所有登录状态？(y/N): ").strip().lower()
                if confirm == 'y':
                    manager.clear_state()
                else:
                    print("操作已取消")
            elif choice == "4":
                backup_dir = input("备份目录路径 (默认: login_states_backup): ").strip()
                if not backup_dir:
                    backup_dir = "login_states_backup"
                manager.backup_states(backup_dir)
            elif choice == "5":
                backup_path = input("备份路径: ").strip()
                if backup_path:
                    manager.restore_states(backup_path)
                else:
                    print("❌ 请提供备份路径")
            elif choice == "6":
                account_input = input("账号ID (留空检查所有): ").strip()
                if account_input:
                    try:
                        account_id = int(account_input)
                        manager.check_state_validity(account_id)
                    except ValueError:
                        print("❌ 请输入有效的账号ID")
                else:
                    manager.check_state_validity()
            elif choice == "7":
                try:
                    account_id = int(input("请输入要注销的账号ID: "))
                    await logout_account(account_id)
                except ValueError:
                    print("❌ 请输入有效的账号ID")
            elif choice == "8":
                try:
                    account_id = int(input("请输入要强制注销的账号ID: "))
                    await force_logout_account(account_id)
                except ValueError:
                    print("❌ 请输入有效的账号ID")
            elif choice == "9":
                confirm = input("确认注销所有账号的登录状态？(y/N): ").strip().lower()
                if confirm == 'y':
                    await logout_all_accounts()
                else:
                    print("操作已取消")
            else:
                print("❌ 无效选择")
    
    # 运行异步主函数
    asyncio.run(async_main())

if __name__ == "__main__":
    main()