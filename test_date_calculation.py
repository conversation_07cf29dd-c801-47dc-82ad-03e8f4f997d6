#!/usr/bin/env python3
"""
测试日期计算逻辑
"""
from datetime import datetime, timedelta

def test_date_calculation():
    """测试30天日期范围计算"""
    print("=" * 50)
    print("测试日期计算逻辑")
    print("=" * 50)
    
    # 模拟今天是2025年7月18日
    today = datetime(2025, 7, 18)
    
    # 计算结束日期（前一天）
    end_date = today - timedelta(days=1)
    
    # 计算开始日期（从结束日期向前29天，总共30天）
    start_date = end_date - timedelta(days=29)
    
    # 计算实际天数
    actual_days = (end_date - start_date).days + 1
    
    print(f"今天: {today.strftime('%Y-%m-%d')}")
    print(f"结束日期: {end_date.strftime('%Y-%m-%d')}")
    print(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
    print(f"实际天数: {actual_days} 天")
    
    # 验证逻辑
    if actual_days == 30:
        print("✅ 日期计算正确")
    else:
        print("❌ 日期计算错误")
    
    print("\n" + "=" * 50)
    print("前端JavaScript等效计算:")
    print("=" * 50)
    
    # 生成JavaScript代码
    js_code = """
const today = new Date();
const endDate = new Date(today);
endDate.setDate(today.getDate() - 1); // 前一天

const startDate = new Date(endDate);
startDate.setDate(endDate.getDate() - 29); // 从结束日期向前29天，总共30天

console.log('今天:', today.toISOString().split('T')[0]);
console.log('结束日期:', endDate.toISOString().split('T')[0]);
console.log('开始日期:', startDate.toISOString().split('T')[0]);

const actualDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
console.log('实际天数:', actualDays);
"""
    
    print(js_code)
    
    print("测试用例:")
    test_cases = [
        datetime(2025, 7, 18),  # 今天
        datetime(2025, 3, 1),   # 3月1日（可能跨月）
        datetime(2025, 1, 15),  # 1月中旬
        datetime(2024, 12, 31), # 年末
    ]
    
    for test_date in test_cases:
        print(f"\n测试日期: {test_date.strftime('%Y-%m-%d')}")
        
        end_d = test_date - timedelta(days=1)
        start_d = end_d - timedelta(days=29)
        days_count = (end_d - start_d).days + 1
        
        print(f"  范围: {start_d.strftime('%Y-%m-%d')} 到 {end_d.strftime('%Y-%m-%d')}")
        print(f"  天数: {days_count}")
        
        if days_count == 30:
            print("  ✅ 正确")
        else:
            print("  ❌ 错误")

if __name__ == "__main__":
    test_date_calculation()
